import yaml, os
from envs._GLOBAL_CONFIGS import CONFIGS_PATH


def get_camera_config(camera_type):
    camera_config_path = os.path.join(CONFIGS_PATH, "_camera_config.yml")

    assert os.path.isfile(camera_config_path), "task config file is missing"

    with open(camera_config_path, "r", encoding="utf-8") as f:
        camera_args = yaml.load(f.read(), Loader=yaml.FullLoader)

    assert camera_type in camera_args, f"camera {camera_type} is not defined"
    return camera_args[camera_type]
