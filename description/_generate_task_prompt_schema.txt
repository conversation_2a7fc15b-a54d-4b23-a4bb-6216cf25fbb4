# schema requirements

## OBJECTIVE
1. Use placeholders in the format {X} for objects, where X is defined in a schema.
2. Ensure all object placeholders ({A-Z}) are included in every instruction, but REFERENCE TO ARMS, INCLUDING arm placeholders ({a-z}) MUST be omitted in 50% of the instructions.
3. Make sure instructions flow naturally when placeholders ({A-Za-z}) are replaced with actual objects or arm notations.