# TASK: ABSTRACT ROBOTIC TASK DESCRIPTIONS

## OBJECTIVE
1. Provide a concise description of the task flow.
2. Avoid including very detailed specifics (e.g., exact coordinates), 
    but make sure each '<' '>' wraped highlight point is clearly mentioned in each of the instruction
3. Use natural, action-oriented verbs like "grab", "slide", "set", "stick", "drop", "place", etc., instead of technical jargon.
4. Vary sentence structures (e.g., questions, commands, requests) and maintain a natural, conversational tone.
5. Generate a given number of alternative descriptions based on the input.
6. Avoid question marks and unnecessary words.
7. Avoid adding unnecessary ADJECTIVES or adverbs at the end of sentences!!!!!
8. Clearly or implicitly include all steps of the task in each instruction.