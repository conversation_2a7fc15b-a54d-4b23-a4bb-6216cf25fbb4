#!/bin/bash

# 获取传入的参数
object_name=${1}
object_id=${2}

# 检查是否提供了足够的参数
if [ -z "$object_name" ]; then
    echo "Error: object_name is required."
    echo "Usage: $0 <object_name> [object_id]"
    exit 1
fi

# 检查 object_id 是否为空
if [ -z "$object_id" ]; then
    # 如果 object_id 为空，传递一个空字符串
    python utils/generate_object_description.py "$object_name" 
else
    # 如果 object_id 不为空，正常传递
    python utils/generate_object_description.py "$object_name" --index "$object_id"
fi