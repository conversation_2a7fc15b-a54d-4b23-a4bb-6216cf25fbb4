list_files=(
    # "adjust_bottle"
    # "beat_block_hammer"
    # "blocks_ranking_rgb"
    # "blocks_ranking_size"
    # "click_alarmclock"
    # "click_bell"
    # "dump_bin_bigbin"
    # "grab_roller"
    # "handover_block"
    # "handover_mic"
    # "lift_pot"
    # "move_can_pot"
    # "move_playingcard_away"
    # "move_stapler_pad"
    # "pick_diverse_bottles"
    # "pick_dual_bottles"
    # "place_a2b_left"
    # "place_a2b_right"
    # "place_bread_basket"
    # "place_bread_skillet"
    # "place_can_basket"
    # "place_cans_plasticbox"
    # "place_container_plate"
    # "place_dual_shoes"
    # "place_cylinder_box"
    # "place_empty_cup"
    # "place_fan"
    # "place_mouse_pad"
    # "place_object_scale"
    # "place_object_stand"
    # "place_phone_stand"
    # "place_shoe"
    # "place_object_basket"
    # "put_bottles_dustbin"
    # "put_object_cabinet"
    # "rotate_qrcode"
    # "scan_object"
    # "shake_bottle_horizontally"
    # "shake_bottle"
    # "stack_blocks_three"
    # "stack_blocks_two"
    # "stack_bowls_three"
    # "stack_bowls_two"
    # "stamp_seal"
    # "turn_switch"
    # "open_laptop"
    # "hanging_mug"
    # "open_microwave"
    # "place_hamburg_frenchfries_tray"
    # "place_object_stand"
    "open_microwave"
    "move_pillbottle_pad"
)
for item in "${list_files[@]}"
do
    bash clear_task_seen_unseen.sh "$item"
    echo "Cleared seen/unseen for task: $item"
    bash gen_task_instruction_templates.sh "$item" 60
done
