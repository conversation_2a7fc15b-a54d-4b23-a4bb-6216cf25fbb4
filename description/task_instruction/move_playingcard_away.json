{"full_description": "use the arm to <pick up the playing card> and <move it away from the table>.For example, if the playing card is on the outward side of the table, you should move it further outward side of the table.", "schema": "{A} notifies the playing card, {a} notifies the arm to grab the playing card", "preference": "num of words should not exceed 10", "seen": ["Move {A} further outward after picking it up.", "Pick up {A} using {a} and shift it outward.", "Lift {A} and relocate it off the table.", "With {a}, pick up {A} and slide it outward.", "Grab {A} and move it further outward.", "Pick up {A} with {a} and place it outward.", "Lift {A} and set it away from the table.", "Use {a} to grab {A} and move it aside.", "Pick up {A} and shift it further outward.", "Using {a}, lift {A} and move it outward.", "Lift {A} and push it off the table.", "Pick up {A} and move it away.", "Move {A} outward from the table.", "Shift {A} off the table to the side.", "Take {A} and place it further outward.", "Slide {A} off the table outward.", "Grab {A} and move it further away.", "Lift {A} and shift it outward.", "Place {A} away from the table outward.", "Move {A} off the table outward.", "Use {a} to grab {A} and move it away.", "Move {A} farther from the table using {a}.", "Grab {A} and take it away from the table.", "Pick up {A} and move it farther outward.", "Use {a} to pick {A} and shift it outward.", "Take {A} and move it away from the table.", "Lift {A} and place it farther away with {a}.", "Shift {A} outward by grabbing it with {a}.", "Pick {A} and move it outward from the table.", "Grab {A} and shift it farther away from the table.", "Move {A} away from its position on the table.", "Grab {A} from the table and shift it outward.", "Lift {A} using {a} and transfer it outward.", "Relocate {A} farther outward using {a}.", "Pick up {A} and place it further away.", "Shift {A} outward after grabbing it.", "Use {a} to pick up {A} and move it.", "Pick {A} and slide it further outward.", "Lift {A} from the table and move it outward.", "Grab {A} using {a} and move it outward.", "Lift {A} and slide it off the table.", "Grab {A} with {a} and move it outward.", "Take {A} and push it further away.", "Use {a} to pick up {A} and push it outward.", "Grab {A} and set it beyond the table.", "Lift {A} with {a} and move it outward.", "Pick {A} from the table and shift it outward.", "Use {a} to pick {A} and move it outward.", "Take {A} and place it beyond the table.", "Grab {A} using {a} and push it outward."], "unseen": ["Use {a} to grab {A} and move it outward.", "Grab {A} and move it off the table.", "Grab {A} and move it outward.", "Pick up {A} and shift it outward.", "Pick up {A} and move it outward.", "Lift {A} from the table and shift it.", "Pick up {A} and move it outward.", "Use {a} to grab {A} and relocate it.", "Pick up {A} and move it outward.", "Use {a} to grab {A} and relocate it."]}