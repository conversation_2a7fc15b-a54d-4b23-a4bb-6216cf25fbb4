{"full_description": "use appropriate arm to place object A on the right of object B", "schema": "{A} notifies the object A, {B} notifies the object B, {a} notifies the arm to grab the object A", "preference": "num of words should not exceed 10.STRESS THE 'right'.", "seen": ["Use {a} to set {A} right of {B}.", "Position {A} to the right of {B}.", "Move {A} to {B}'s right position.", "Grab {A} with {a} and place it right of {B}.", "Ensure {A} is placed on the right of {B}.", "Grab {A} using {a} and set it to {B}'s right.", "Move {A} and place it carefully to {B}'s right.", "Take {A} with {a} and position it right of {B}.", "Set {A} neatly on the right side of {B}.", "Grab {A} using {a}, then move it to {B}'s right.", "Set {A} on the right side of {B}.", "Use {a} to position {A} to {B}'s right.", "Move {A} and place it to {B}'s right.", "Put {A} using {a} on the right of {B}.", "Set {A} right next to {<PERSON>}'s right side.", "Grasp {A} with {a} and move it to {B}'s right.", "Place {A} directly on {<PERSON>}'s right.", "Using {a}, position {A} to the right of {B}.", "Set {A} carefully on {B}'s right side.", "Grab {A} using {a} and place it right of {B}.", "Use {a} to place {A} right of {B}.", "Position {A} directly to the right of {B}.", "Move {A} and set it to {B}'s right.", "With {a}, move {A} to {B}'s right.", "Set {A} down on {B}'s right side.", "Bring {A} to the right position of {B}.", "Using {a}, position {A} to the right of {B}.", "Place {A} precisely to the right of {B}.", "Move {A} using {a} to {B}'s right position.", "Set {A} properly on the right of {B}.", "Set {A} on {B}'s right side", "Position {A} to {B}'s right using {a}", "Shift {A} to the right of {B}", "Move {A} right of {B} with {a}", "Stick {A} on {B}'s right side", "Place {A} using {a} at {B}'s right", "Position {A} at the right side of {B}", "Move {A} to {B}'s right using {a}", "Put {A} at the right of {B}", "Set {A} using {a} at {B}'s right", "Use {a} to place {A} right of {B}.", "Position {A} to the right of {B}.", "Move {A} to the right beside {B}.", "Ensure {A} is on the right of {B}.", "Direct {a} to place {A} right of {B}.", "Put {A} exactly to the right of {B}.", "Use {a} to set {A} right of {B}.", "Place {A} on the right of {B}.", "Grab {A} with {a} and set right of {B}.", "Make sure {A} is placed right of {B}."], "unseen": ["Put {A} to the right of {B}.", "Place {A} on {B}'s right side.", "Put {A} to the right of {B}.", "Grab {A} and place it right of {B}.", "Place {A} on the right of {B}.", "Set {A} to the right of {B}.", "Put {A} to the right of {B}", "Place {A} rightward of {B} using {a}", "Put {A} to the right of {B}.", "Set {A} on the right of {B}."]}