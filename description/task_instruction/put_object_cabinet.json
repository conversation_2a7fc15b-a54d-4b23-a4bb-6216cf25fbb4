{"full_description": "use {a} to open the cabinet's drawer, and use another arm to put the object on the table to the drawer.", "schema": "{A} notifies the object, {B} notifies the cabinet (use 'the drawer of {B}' or '{B}'s drawer'), {a} notifies the arm to open the drawer. Another arm to put the object into the drawer use literal 'the other arm'", "preference": "num of words should not exceed 20. Degree of detail avg is 8.If {a} is mentioned in one description, 'the other arm' or 'another arm' must be mentioned in another description.Or otherwise both arm should not be mentioned. BUT {A} and {B} should always be mentioned in each description", "seen": ["Open {B}'s drawer and place {A} inside it.", "Access {B}'s drawer and set {A} inside.", "Use {a} to open the drawer of {B} and then the other arm to place {A} inside.", "Pull open {B}'s drawer with {a} and use the other arm to insert {A}.", "Pull open {B}'s drawer and put {A} inside.", "Open the drawer of {B} and place {A} into it.", "Use {a} to open {B}'s drawer before the other arm places {A} inside.", "With {a}, open {B}'s drawer and let the other arm drop {A} inside.", "Open {B}'s drawer and move {A} into it.", "Slide open {B}'s drawer and set {A} inside.", "Open {B}'s drawer and transfer {A} into it.", "Pull open the drawer of {B} and place {A} inside.", "Use {a} to open the drawer of {B} and let the other arm set {A} inside.", "Slide open {B}'s drawer with {a} and use the other arm to put {A} into it.", "Open {B}'s drawer and carefully drop {A} into it.", "Pull open the drawer of {B} and move {A} into it.", "With {a}, open the drawer of {B} and place {A} inside using the other arm.", "Use {a} to open {B}'s drawer, then transfer {A} into it with the other arm.", "Open the drawer of {B} and set {A} inside.", "Open {B}'s drawer and move {A} into it.", "Use {a} to slide open {<PERSON>}'s drawer, then the other arm to put {A} in.", "Unlock the drawer of {B} and drop {A} into it.", "Use {a} to open {B}'s drawer and use another arm to stick {A} inside.", "Pull open {B}'s drawer and place {A} inside it.", "Use {a} to open the drawer of {B}, then use the other arm to set {A} inside.", "Slide out the drawer of {B} and stick {A} inside.", "With {a}, open {B}'s drawer, then place {A} in using another arm.", "Open the drawer of {B} and drop {A} inside.", "Use {a} to pull {<PERSON>}'s drawer open and use the other arm to stick {A} inside.", "Slide open the drawer of {B} and set {A} into it.", "Pull open {B}'s drawer and place {A} into it.", "Open the drawer of {B} and move {A} into it.", "Use {a} to slide open {<PERSON>}'s drawer, then use the other arm to transfer {A} inside.", "Unlock {B}'s drawer with {a} and set {A} inside using the other arm.", "Slide open {B}'s drawer and drop {A} inside.", "Pull open {B}'s drawer and place {A} inside.", "Use {a} to pull the drawer of {B} open and the other arm to put {A} inside.", "Open {B}'s drawer with {a} and place {A} inside using the other arm.", "Open the drawer of {B} and move {A} into the drawer.", "Pull {B}'s drawer open and transfer {A} into it.", "Slide open the drawer of {B} before putting {A} inside.", "Put {A} into {B}'s drawer once it is opened.", "Use {a} to slide open {<PERSON>}'s drawer and move {A} in with the other arm.", "Place {A} inside the drawer of {B} after opening it using {a}.", "Open the drawer of {B} before placing {A} inside it.", "Set {A} into {B}'s drawer after opening it.", "Use {a} to open {B}'s drawer and drop {A} inside with the other arm.", "Slide open the drawer of {B} using {a} and place {A} into it with the other arm.", "Open {B}'s drawer first, then place {A} inside.", "Place {A} into the drawer of {B} after sliding it open."], "unseen": ["Use {a} to open {B}'s drawer and the other arm to place {A} inside.", "Open {B}'s drawer with {a} and use the other arm to put {A} inside.", "Open the drawer of {B} with {a} and place {A} inside using the other arm.", "Use {a} to pull open {<PERSON>}'s drawer and the other arm to move {A} into it.", "Open {B}'s drawer with {a} and place {A} inside using the other arm.", "Pull out the drawer of {B} and set {A} into it.", "Open the drawer of {B} with {a}, then the other arm places {A} inside.", "Open {B}'s drawer and set {A} inside with the other arm.", "Open {B}'s drawer using {a} and place {A} inside with the other arm.", "Set {A} in the drawer of {B} after opening it with {a}."]}