{"full_description": "place the container onto the plate", "schema": "{A} notifies the plate to be placed onto, {B} notifies the arm to manipulate the container, {a} notifies the arm to catch the container", "preference": "num of words should not exceed 10. Degree of detail avg 5", "seen": ["Move the container with {B} to {A}.", "Use {B} to place the container onto {A}.", "Transfer the container and drop it on {A}.", "Move and release the container onto {A}.", "Grab the container with {a} and set on {A}.", "Pick up the container with {a}, place it on {A}.", "Secure the container, move it, and set it on {A}.", "Carry the container and release it onto {A}.", "Position the container using {B} onto {A}.", "Lift the container with {B}, then place it onto {A}.", "Lift container and drop it onto {A}.", "Move the container to {A} and set it down.", "Use {B} to grab the container and place it on {A}.", "Catch the container, align it, place on {A}.", "Secure container with {B}, move it, place on {A}.", "Lift the container and position it on {A}.", "Grab container using {B}, transfer it to {A}.", "Place the container directly on {A}.", "Catch {a}, move container over {A}, drop it.", "Lift container toward {A} and carefully set it down.", "Drop the container onto {A} using {B}", "Deliver the container to {A} and release", "Set the container down on {A} carefully", "Place the container flat on {A} now", "Move the container above {A} and drop it", "Lift the container and set it onto {A}", "Position {B} to lower the container on {A}", "Move {B} and drop the container onto {A}", "Use {B} to place the container into {A}", "Guide the container with {B} onto {A}", "Stick the container onto {A} using {B}.", "Slide the container into position on {A}.", "Place the container onto {A} with {B}.", "Align the container and set onto {A}.", "Grab the container using {a} and place it onto {A}.", "Catch the container with {B} and drop onto {A}.", "Lift the container and set it onto {A}.", "Move the container and stick it onto {A}.", "Position the container onto {A} using {B}.", "Shift the container and place it on {A}.", "Place the container onto {A}.", "Catch the container and set it on {A}.", "Drop the container onto {A}.", "Use {a} to lower the container onto {A}.", "Stick the container on top of {A}.", "Grab the container and place on {A}.", "Position the container and set on {A}.", "Slide the container to {A} using {a}.", "Place the container firmly onto {A}.", "Hold the container with {a} and drop onto {A}."], "unseen": ["Place the container onto {A}.", "Set the container on top of {A}.", "Catch {a}, move it over {A}, set it down.", "Grab container with {B}, place it on {A}.", "Move {B} to set the container on {A}", "Place the container onto {A} with {B}", "Set the container on {A} with {B}.", "Move and drop the container onto {A}.", "Set the container on {A}.", "Lower the container onto {A} using {a}."]}