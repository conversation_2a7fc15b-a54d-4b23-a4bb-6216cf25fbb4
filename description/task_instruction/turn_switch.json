{"full_description": "use the robotic arm to click the switch", "schema": "{A} notifies the switch, {a} notifies the arm to click the switch", "preference": "num of words should not exceed 7.Degree of detail avg 5", "seen": ["Press {A} using {a}.", "Activate {A} with {a}.", "Move to {A} and click it.", "Click {A} directly.", "Press {A} to activate.", "Engage {A} with a press.", "Interact with {A}.", "Push {A} via {a}.", "Toggle {A} with {a}.", "Engage {A} using {a}.", "Engage {a} to press {A}", "Press {A} directly", "Use {a} to interact with {A}", "Trigger {A} without referencing {a}", "Command {a} to engage {A}", "Set the switch at {A}", "Activate {A} with robotic precision", "Direct {a} to operate {A}", "Switch {A} to its active state", "Deploy {a} to press {A}", "Engage {A} using the robotic arm", "Press {A} using robotic control", "Click {A} with robotic precision", "Activate {A} directly", "Press {A} via automation", "Click {A} using automated tools", "Deploy {a} to operate {A}", "Use {a} to engage {A}", "Set {a} to press {A}", "Operate {A} with {a}", "Activate {A} via {a}", "Trigger {A} with precision", "Tap {A} to engage it", "Locate and press {A}", "Initiate {A} by clicking", "Engage {A} using {a}", "Approach and tap {A} via {a}", "Click on {A} precisely", "Interact with {A} directly", "Press {A} carefully with {a}", "Directly interact with {A}", "Activate the {A} using {a}", "Press {A} with {a}", "Click {A} to activate it", "Operate {a} to engage {A}", "Engage with {A} manually", "Trigger {A} using {a}", "Locate and press {A}", "Use {a} for switching {A}", "Activate {A} by pressing it"], "unseen": ["Click {A} with {a}.", "Use {a} to press {A}.", "Click {A} using {a}", "Activate the switch at {A}", "Direct {a} to click {A}", "Click {A} with {a}", "Click {A} with {a}", "Press {A} using {a}", "Move and click the {A}", "Use {a} to press {A}"]}