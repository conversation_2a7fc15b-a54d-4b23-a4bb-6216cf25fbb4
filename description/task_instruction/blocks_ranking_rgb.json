{"full_description": "<Place> the red block, green block, and blue block <in the order> of red, green, and blue from left to right, <placing in a row>.", "schema": "{A} notifies the red block, {B} notifies the green block, {C} notifies the blue block, {a} notifies the arm to grab red block, {b} notifies the arm to grab green block, {c} notifies the arm to grab blue block", "preference": "num of words should not exceed 30", "seen": ["Set {A}, {B}, and {C} side by side in the order of {A}, {B}, then {C}.", "Use {a} to grab {A} and position it on the left, then {b} for {B}, and {c} for {C}.", "Grab {A} using {a}, {B} with {b}, and {C} with {c}, aligning them consecutively in a row.", "Start with {A}, followed by {B} and {<PERSON>}, placing them in order left to right.", "Position {A}, {B}, and {C} from left to right in the specified sequence.", "Grab {A} with {a}, place it leftmost, then {B} using {b}, followed by {C} with {c}.", "Begin by placing {A} on the left, then add {B} to the right, and finish with {C}.", "Use {a} for {A}, then {b} for {B}, and {c} for {C}, arranging them left to right.", "Arrange {A}, {B}, and {<PERSON>} in sequence from leftmost to rightmost positions.", "Pick up {A}, {B}, and {C sequentially and place them in a row from left to right.", "Position {A} first, followed by {B}, and end with {C} in a row from left to right.", "Grab {A}, place it on the leftmost spot, add {B} to the middle, and set {C} last.", "Arrange {A}, {B}, and {C} in a row using {a}, {b}, and {c}, keeping the left-to-right order.", "Pick up {A}, position it on the left, add {B} next to it, and finally set {C} on the far right.", "Use {a} to grab {A}, set it on the left, follow with {b} for {B}, and end with {c} for {C}.", "Arrange {A} at the left edge, {B} to the right of it, and {C} next to {B} forming a row.", "Pick up {A} using {a}, set it on the left, repeat the placement with {b} for {B}, and {c} for {C}.", "First, place {A} on the far left, then {B} in the middle, and finish with {C} on the right.", "Grab {A}, set it on the leftmost side, add {B} to its right, and complete with {C} on the far right.", "Using {a}, {b}, and {c}, arrange {A}, {B}, and {C} sequentially from left to right in a single row.", "Place {A} to the left, {<PERSON>} in the middle, and {C} on the right in a linear fashion.", "Position {A} first, {<PERSON>} second, and {<PERSON>} third in a row from left to right.", "Using {a}, {b}, and {c}, arrange {A}, {B}, and {C} from left to right in the given order.", "With {a}, {b}, and {c}, place {A}, {B}, and {C} sequentially from left to right as red, green, blue.", "Arrange {A}, {B}, and {<PERSON>} from left to right using {a}, {b}, and {c} in the order red, green, blue.", "First, grab {A}, then {B}, then {C} with {a}, {b}, and {c}, and place them in order left to right.", "Position {A}, {B}, and {C} on a surface from left to right in the order red, green, blue.", "Grab {A}, {B}, and {C}, and place them steadily from left to right in the order red, green, blue.", "Using {a}, {b}, and {c}, set {A}, {B}, and {C} from left to right in the sequence red, green, blue.", "Put {A} first, {B} next, and {<PERSON>} last in a straight line from left to right, using {a}, {b}, and {c}.", "Grab and place {A} first, followed by {B}, and finish with {C} in a row.", "Position {A}, {B}, and {C} in a left-to-right sequence, forming a row.", "First, place {A}, then {B} beside it, and finally {C} next to {B}.", "Using {a}, {b}, and {c}, align {A}, {B}, and {C} in a row from left to right.", "Line up the objects in the order {A}, {B}, and {C}, starting from the left.", "Pick up {A}, {B}, and {<PERSON>} in sequence and position them in a left-to-right row.", "Start with {A} on the left, add {B} next to it, and then place {C} on the right.", "{A}, then {B}, and finally {C} should be placed in a row from left to right.", "With {a}, {b}, and {c}, arrange {A}, {B}, and {C} sequentially in a horizontal row.", "Place {A} first, set {B} beside it, and complete the row by positioning {C}.", "Use {a} to place {A} on the left, then use {b} for {B} beside {A}, and finally {c} for {C} next to {B}.", "Grab {A}, position it on the left. Next, grab {B} to place beside {A}, then grab {C} and set it to the right of {B}.", "First, use {a} to grab {A} and place it on the left. Then grab {B} with {b} and position it next to {A}, followed by grabbing {C} with {c} and placing it next to {B}.", "Start with {A} on the left, followed by {B} next to {A}, and end with {C} on the far right.", "Pick {A} using {a}, place it far left. Then grab {B} with {b}, position next to {A}. Lastly, grab {C} using {c} and set right of {B}.", "Position {A} in the first spot, then set {B} to the right of {A}, and finish by placing {C} to the right of {B}.", "Move {A} with {a} to the far left, {B} with {b} just beside {A}, and finally {C} with {c} next to {B}.", "Start by grabbing {A}, place it on the leftmost side. Next, position {B} beside {A}, and finally set {C} to the right of {B}.", "Use {a} for {A} to set it leftmost, continue with {b} to position {B} next to {A}, and finish by placing {C} using {c} to the right of {B}.", "Place {A} first on the far left, {B} next to it, and {C} last on the far right."], "unseen": ["Arrange {A}, {B}, and {<PERSON>} from left to right in a row.", "Place {A}, {B}, and {C} sequentially in a row, starting with {A}.", "Place {A} on the left, then set {B} to its right and {C} next to {B}.", "Use {a} to arrange {A} first, then use {b} for {B}, and finally use {c} for {C} in a row.", "Set {A}, {B}, and {<PERSON>} in a row from left to right in the order red, green, blue.", "Arrange {A}, {B}, and {<PERSON>} in a row with {A} on the left, {B} in the middle, and {<PERSON>} on the right.", "Set {A} on the left, then {<PERSON>} in the center, and finally {C} on the right.", "Arrange {A}, {B}, and {C} side by side, starting with {A} on the left.", "Pick up {A}, set it on the left. Then grab {B}, position it next to {A}. Finally, place {C} to the right of {B}.", "Start by placing {A} to the left, followed by {B} next to {A}, and end with {C} on the far right."]}