{"full_description": "use arms to grab the bottles and put them into the dustbin to the left of the table", "schema": "{A} notifies the first bottle, {B} notifies the second bottle, {C} notifies the third bottle, {D} notifies the dustbin", "preference": "num of words should not exceed 20. Degree of detail avg is 6.", "seen": ["Take {A}, {B}, and {C} one at a time and place them into {D}.", "Grab {A} and {B}, put them into {D}, and repeat for {C}.", "Use the arms to transfer {A}, {B}, and {C} into {D} one by one.", "Move {A}, {B}, and {C} to {D} sequentially using the arms.", "Drop {A}, {B}, and {C} into {D}, handling them one at a time.", "With arms, place {A}, {B}, and {C} into {D} step by step.", "Carefully grab {A}, {B}, and {C} and position them in {D}.", "Using arms, pick up {A}, {B}, and {C}, dropping each into {D}.", "Place {A}, {B}, and {C} into {D} after taking them individually.", "Transfer {A}, {B}, and {C} into {D} using your arms, one by one.", "Using arms, grab {A} and put it in {D}, repeat for {B} and {C}.", "Take {A}, drop it in {D}, then move {B} and {C} to {D}.", "Grab {A}, place it in {D}, then grab {B} and {C} for {D}.", "Use arms to pick {A}, drop it in {D}, repeat for {B} and {C}.", "Pick {A} and move it into {D}, repeat for {B} and {C}.", "Using arms, grab {A} and {B}, then place them into {D} along with {C}.", "Grab {A}, put it in {D}, follow by moving {B} and {C} there too.", "Take {A} with arms, put it in {D}, repeat the same for {B} and {C}.", "Pick {A}, drop it into {D}, then move {B} and {C} into {D}.", "Using arms, grab {A} and place it in {D}, do the same for {B} and {C}.", "Move {A}, {B}, and {C} into {D} one by one.", "Grab each of {A}, {B}, and {C} and drop them into {D}.", "Use the arms to move {A} into {D}, then repeat for {B} and {C}.", "Place {A}, {B}, and {C} in {D} using the arms.", "Transfer {A}, {B}, and {C} into {D} step by step.", "Put {A} into {D}, followed by {B} and {C}.", "Use arms to pick {A}, {B}, and {C}, dropping them into {D}.", "Move {A} to {D}, then {B}, and finally {C}.", "Grab {A}, {B}, and {C} sequentially, placing each into {D}.", "Pick {A}, {B}, and {<PERSON>} one at a time and put them into {D}.", "Use arms to move {A}, {B}, and {C} into {D}.", "Start with {A}, grab it and drop it into {D}, repeat for {B} and {C}.", "Place {A}, {B}, and {C}, one at a time, into {D}.", "Begin with {A}, move it to {D}, then continue with {B} and {C}.", "Lift {A}, {B}, and {C}, dropping each into {D} sequentially.", "Grab {A}, drop it in {D}, do the same for {B} and {C}.", "Pick up {A}, {B}, and {C}, moving them one by one into {D}.", "Use the arms to transfer {A}, {B}, and {C} into {D} step by step.", "Move {A} into {D}, then do the same for {B} and {C}.", "Carry {A} first, drop it into {D}, repeat with {B} and {C}.", "Use arms to pick {A}, {B}, {C}, and place them one by one in {D}.", "Transfer {A}, {B}, and {C} to {D} located to the table's left.", "Grab {A}, {B}, and {C} and put them sequentially into {D}.", "Use arms to grab {A}, {B}, {C}, and set them into {D} to the left.", "Pick {A}, {B}, and {<PERSON>} from the table and place them inside {D}.", "Use arms to move {A}, {B}, and {C} to {D} on the table's left.", "Place {A}, {B}, and {C} into {D} one after another.", "Grab {A}, {B}, {C}, and drop them into {D} to the left of the table.", "Use arms to pick {A}, {B}, and {C} and place them in {D}.", "Take {A}, {B}, and {C} and place them into {D} on the left."], "unseen": ["Pick up {A} and toss it into {D}; repeat for {B} and {C}.", "Use arms to grab {A}, {B}, and {C} and drop them into {D}.", "Pick {A}, drop it into {D}, repeat for {B} and {C}.", "Grab {A} and place it in {D}, then do the same for {B} and {C}.", "Pick up {A} and drop it into {D}, do the same for {B} and {C}.", "Use arms to grab {A}, {B}, and {C}, placing each into {D}.", "Pick up {A}, drop it into {D}, then repeat for {B} and {C}.", "Take each bottle {A}, {B}, {C} one by one and place them in {D}.", "Pick up {A}, {B}, and {C} one by one and drop them into {D}.", "Move {A}, {B}, and {C} into {D} on the left of the table."]}