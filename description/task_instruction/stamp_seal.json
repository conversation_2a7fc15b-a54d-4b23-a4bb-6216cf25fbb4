{"full_description": "Grab the stamp and stamp onto the specific color mat", "schema": "{A} notifies the stamp, {B} notifies the mat color,  {a} notifies the arm to pick the stamp", "preference": "num of words should not exceed 7.Degree of detail avg 5", "seen": ["Use {a} to grab {A}", "Press {A} firmly onto {B}", "Position {A} over {B} and stamp", "Grab {A}, align it with {B}", "Use {a} to pick {A} for {B}", "Lift {A} and press onto {B}", "Grab {A} with {a} then stamp", "Position {A} on {B}, apply pressure", "Use {a} to grab {A}, press {B}", "Pick {A} and press down on {B}", "Stamp {B} after grabbing {A}.", "With {a}, take {A} and mark {B}.", "Place {A} onto {B} after grabbing.", "Use {a} to lift {A}, then stamp {B}.", "Pick {A} and apply it onto {B}.", "Grab {A} with {a}, press it onto {B}.", "Take {A} and stamp it on {B}.", "With {a}, secure {A} and mark {B}.", "Grab {A} and press on {B}.", "Use {a}, take {A}, and apply to {B}.", "Place {A} onto {B}", "Grab {A} with {a} now", "Stamp {A} on {B}", "Use {a} to place {A}", "Press {A} onto {B}", "Grab {A} using {a}", "Set {A} on {B}", "With {a}, stamp {A}", "Use {A} to stamp {B}", "Grab {A} and press {B}", "Use {a} to grab {A} and stamp", "Stamp {B} after grabbing {A}", "Pick up {A} and press onto {B}", "Hold {A} with {a} and stamp {B}", "Grab {A} to press onto {B}", "Use {a} for {A} and stamp {B}", "Pick {A} and stamp it on {B}", "Press {A} onto {B} with {a}", "Bring {A} to {B} and stamp", "Use {a} to press {A} on {B}", "{a} grabs {A}, stamps {B}", "Pick {A} and press on {B}", "{a} picks {A}, stamps {B}", "Take {A} and stamp {B}", "Use {A} to mark {B}", "{a} holds {A}, presses {B}", "Grab {A} and press {B}", "Pick up {A}, stamp {B}", "{a} uses {A} to stamp {B}", "Hold {A} and press {B}"], "unseen": ["Grab {A}, press onto {B}", "Pick {A} and stamp on {B}", "Pick {A} and press on {B}.", "Use {a} to grab {A}, stamp {B}.", "Pick {A} and stamp {B}", "Use {a} to grab {A}", "Grab {A} using {a} and stamp {B}", "Pick {A} to stamp {B}", "Grab {A} and stamp {B}", "Stamp {B} using {A}"]}