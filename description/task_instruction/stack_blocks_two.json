{"full_description": "there are two blocks on the table, the color of the blocks is <red, green>, <move the blocks to the center of the table>, and <stack the geen block on the red block>", "schema": "{A} notifies the red block, {B} notifies the green block, {a} notifies the arm to manipulate the red block, {b} notifies the arm to manipulate the green block", "preference": "num of words should not exceed 20. Degree of detail avg 8", "seen": ["{a} grabs {A}, places it in the center, {b} stacks {B} on {A}.", "Shift {A} and {B} to the center and stack {B} on {A}.", "Move {A} to the center, then {b} stacks {B} on {A}.", "{a} relocates {A} and {b} moves {B}, then stacks {B} on {A}.", "Set {A} and {B} at the center, stacking {B} atop {A}.", "Place {A} and {B} centrally, then stack {B} on {A}.", "Move {A} and {B} to the center, then stack {B} above {A}.", "{a} takes {A}, sets it in the center, {b} places {B} on {A}.", "Relocate {A} and {B} to the center, then layer {B} over {A}.", "{a} moves {A} to the center, {b} stacks {B} onto {A}.", "Shift {A} and {B} to the middle, then stack {B} over {A}.", "Grab {A} using {a}, position it at the center, and do the same for {B} using {b}, stacking {B} on {A}.", "Pick up {A} with {a} and {B} with {b}, move both to the table's center and stack {B} over {A}.", "Use {a} to move {A} and {b} to move {B} to the center, then place {B} onto {A}.", "Relocate {A} and {B} to the table's midpoint, and stack {B} on top of {A}.", "Pick up {A} and {B}, set them at the center, then position {B} above {A}.", "Place {A} in the center, then move {B} there and stack {B} on {A}.", "Utilize {a} to grab {A} and {b} to grab {B}, move them to the middle, and stack {B} above {A}.", "Set {A} at the center, then stack {B} over {A} after placing it alongside.", "Move {A} and {B} to the center, using {a} and {b}, and stack {B} on top of {A}.", "Shift {A} and {B} to the center, then position {B} on {A}.", "Use {a} to move {A}, then {b} to stack {B} on {A} at the center.", "Place {A} and {B} at the center and then put {B} on {A}.", "Move {A} using {a}, stack {B} on {A} after centering them.", "Center {A} and {B} on the table and stack {B} on top of {A}.", "Use {b} to center {B}, place it on {A} after moving them to the center.", "Set {A} and {B} at the table's center, then stack {B} on {A}.", "Move {A} to the center using {a}, then stack {B} on {A} using {b}.", "Place {A} and {B} together at the center, then stack {B} on top.", "Use {a} for {A}, then stack {B} on {A} after placing both at the center.", "Use {a} to move {A} and {b} to stack {B}.", "Bring {A} to the center, then stack {B} over it.", "Relocate {A} to the middle and place {B} on it.", "Move {A} with {a}, then stack {B} on it using {b}.", "Position {A} at the center and stack {B} above it.", "Use {a} to center {A}, then stack {B} with {b}.", "Shift {A} to the center and place {B} on top.", "Use {a} to move {A} and {b} to position {B}.", "Move {A} to the middle and put {B} on top of it.", "Center {A} using {a}, then stack {B} with {b} on top.", "Move {A} and {B} to the center; stack {B} on top of {A}.", "Grab {A}, set it in the center, then stack {B} over it.", "Use {a} for {A}, place it in the center, then use {b} to stack {B} above {A}.", "Position {A} and {B} at the center, then stack {B} on {A}.", "Move {A} and put it in the center; stack {B} on top using {b}.", "Set {A} in the center, then position {B} on top of it.", "Use {a} to place {A} in the center; stack {B} on it with {b}.", "Put {A} in the center, then add {B} above it.", "Move {A} to the middle, use {b} to stack {B} right on top.", "Place {A} and {B} in the center, then stack {B} above {A}."], "unseen": ["Move {A} and {B} to the center, then stack {B} on {A}.", "Bring {A} and {B} to the center, then position {B} above {A}.", "Take {A} and {B}, move them to the table's center, then stack {B} on {A}.", "Move {A} and {B} to the center and place {B} atop {A}.", "Move {A} and {B} to the table's center, then stack {B} on {A}.", "Grab {A}, place it at the center, then stack {B} on {A}.", "Place {A} in the middle, then stack {B} on it.", "Move {A} to the center, then put {B} on top.", "Place {A} in the center, then stack {B} on it.", "Use {a} to move {A} to the center, then set {b} and stack {B} on {A}."]}