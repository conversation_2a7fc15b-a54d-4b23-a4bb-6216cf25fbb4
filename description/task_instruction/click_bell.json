{"full_description": "click the <bell's top center> on the table", "schema": "{A} notifies the bell, {a} notifies the arm to click the bell", "preference": "num of words should not exceed 10", "seen": ["Press <bell's top center> using {a} on the table", "Instruct {a} to press <bell's top center>", "Push <bell's top center> on the table", "Click {A}'s <top center> using {a}", "Make {a} press <bell's top center>", "Press the <bell's top center> directly", "Direct {a} to click <bell's top center>", "Push {A}'s <top center> on the table", "Click <bell's top center> using {a}", "Press <bell's top center> placed on the table", "Press the center top of {A}.", "Command {a} to press {A}'s top.", "Click at the bell's top center.", "Direct {a} to touch {A}'s top.", "Press down on the bell's top.", "Guide {a} to click the bell's top.", "Click the designated center of {A}.", "Request {a} to press the bell's top.", "Press the specified top area of {A}.", "Ask {a} to interact with {A}'s top.", "Press the center of {A} using {a}.", "Click the bell's center on the table.", "Tap {A}'s top center with {a}.", "Tap the top center of {A}.", "Press {A}'s top center on the table.", "Click using {a} on {A}'s center.", "Push the center of {A} using {a}.", "Push the bell's center on the table.", "Press down {A}'s top center gently.", "Press down the top of {A} using {a}.", "Click the top center of {A} on table.", "Direct {a} to click the top of {A}.", "Pinpoint {A} and click its top center.", "Have {a} click at {A}'s top center.", "Press the top center of {A} on table.", "Make {a} interact with {A}'s top center.", "Click {A} at its top center on table.", "Guide {a} to click {A}'s top center.", "Locate {A} and click its top center.", "Use {a} to press {A}'s top section.", "Engage the top center of the bell.", "Click {A}'s top center using {a}.", "Press the bell's top center on the table.", "Tap {A}'s top center with {a}.", "Touch the bell at its top center.", "Use {a} to touch {A}'s top center.", "Engage the bell's top center gently.", "Activate {A} by pressing its top center.", "Press {A}'s top center with {a} firmly.", "Tap the bell's top center on the table."], "unseen": ["Click the <bell's top center> on the table", "Tap the <bell's top center> placed on the table", "Click the top center of {A}.", "Direct {a} to click {A}'s top.", "Click {A}'s top center on the table.", "Use {a} to press {A}'s top center.", "Find {A} and click its top center.", "Use {a} to press {A}'s top center.", "Click the bell at its top center.", "Use {a} to press {A}'s top center."]}