{"full_description": "Use dual arm to pick and place cans into plasticbox", "schema": "{A} notifies the left can, {B} notifies the plasticbox, {C} notifies right can", "preference": "num of words should not exceed 15", "seen": ["Use both arms to move {A} and {C} into {B}.", "Lift {A}, put it in {B}, then handle {C} similarly.", "With both arms, transfer {A} and {C} into {B}.", "Pick {A}, place it inside {B}, follow the same for {C}.", "Move {A} and {C} one at a time into {B}.", "Use dual arms to pick {A} and {C}, placing both in {B}.", "Place {A} in {B}, follow with {C} using each arm.", "Transfer {A} to {B}, then transfer {C} to {B}.", "First move {A} to {B}, then move {C} into {B}.", "Use your arms to set {A} and {C} gently into {B}.", "Move {A} to {B} and repeat with {C}.", "Use both arms to place {A} and {C} inside {B}.", "Pick {A}, place it in {B}, then pick {C} and place it in {B}.", "Grip {A} and insert it into {B}, then repeat for {C}.", "Move {A} and {C} into {B} using separate arms.", "Transfer {A} to {B}, then transfer {C} to {B}.", "Place {A} and {C} into {B} using both arms.", "Use arms to set {A} and {C} into {B}.", "Pick and drop {A} and {C} into {B}.", "Lift {A}, place it in {B}, then repeat for {C}.", "Use both arms to move {A} and {C} into {B}", "Lift {A} and {C}, then set them inside {B}", "Pick {A} and {<PERSON>} using both arms and put them in {B}", "Place {A} and {C} into {B} with dual arms", "Move {A} and {C} together into {B}", "Transfer {A} and {C} into {B} using dual arms", "Pick and drop {A} and {C} into {B} together", "Using dual arms, place {A} and {C} inside {B}", "Lift {A} and {C}, and stick them into {B}", "Drop {A} and {C} into {B} with both arms", "Use both arms to grab {A} and {C}, place them in {B}.", "Grab {A}, insert it into {B}, then grab {C} and repeat.", "Place {A} and {C} in {B} using both arms.", "Lift {A} into {B}, then {C} to {B} without delay.", "Pick up {A} using one arm, set it in {B}, repeat for {C}.", "Use arms to pick {A}, drop it in {B}, repeat for {C}.", "Identify {A}, place it in {B}, do the same for {C}.", "Grab {A}, transfer it to {B}, then repeat with {C}.", "Both arms lift {A}, drop into {B}, repeat for {C}.", "Pick {A} and {<PERSON>}, put them together inside {B}.", "Use both arms to place {A} and {C} into {B}.", "First pick {A}, place it in {B}, then repeat for {C}.", "Place {A} into {B}, then lift {C} and set it into {B}.", "Use the arms to transfer {A} and {C} into {B}.", "Move {A} into {B}, then pick and drop {C} into the same box.", "Use both arms, position {A} and {C} within {B}.", "Begin with {A}, place it in {B}, finish with {C} into {B}.", "Utilize the arms to deposit both {A} and {C} into {B}.", "Transfer {A} into {B}, then position {C} within {B}.", "Employ both arms to pick {A} and {C}, and place them inside {B}."], "unseen": ["Pick {A}, place it into {B}, then repeat with {C}.", "Grab {A}, drop it in {B}, and do the same for {C}.", "Pick {A} and place it into {B}. Then do the same for {C}.", "Grab {A}, drop it into {B}. Repeat for {C}.", "Grab {A} and {C} and place them in {B}", "Pick up {A} and {C}, drop them together into {B}", "Pick {A}, set it in {B}, then repeat with {C}.", "Lift {A} and {C}, drop both into {B}.", "Move {A} into {B}, then move {C} into {B}.", "Grab {A} and drop it into {B}, then do the same for {C}."]}