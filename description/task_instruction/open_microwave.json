{"full_description": "Use one arm to open the microwave.", "schema": "{A} notifies the microwave, {a} notifies the arm to open the microwave", "preference": "num of words should not exceed 15", "seen": ["Use {a} to pull open the door of {A}.", "Pull the handle to open {A}.", "Grab the handle and open {A} with {a}.", "Open {A} by grasping its handle.", "Pull open the door of {A} using {a}.", "Pull the door of {A} to open it.", "Use {a} to grab and open {A}.", "Open {A} by pulling its door with {a}.", "Open {A} by pulling its handle.", "Pull the handle of {A} to open it.", "Pull the door to open {A}.", "Grab the handle and open {A}.", "Use {a} to pull {A} open.", "Engage {a} to open {A}.", "Pull and open {A}.", "Pull the microwave door to open.", "Open {A} by pulling with {a}.", "Use the handle to open {A}.", "Engage {A} using {a} to open.", "Pull the handle to open {A}.", "Open {A} using {a}.", "Get {A} open.", "Access the inside by opening {A}.", "Activate the door to open {A}.", "Pull to open {A}.", "Grip {A}'s handle and pull it open.", "Handle {A} and open it with {a}.", "Use {a} to engage {A}'s opening mechanism.", "Engage {A}'s handle and open it.", "Utilize {a} to pull {A} open.", "Open the microwave door by using {a}.", "Grip and pull the microwave door open.", "Pull the handle to open the microwave.", "Use {a} to open the microwave door.", "Open the microwave door by pulling it.", "Lift the microwave door with {a} to open it.", "Use {a} to pull the microwave door open.", "Pull open the microwave using its handle.", "Grab the handle and open the microwave door.", "Use {a} to grip and open the microwave.", "Open {A} by pulling its handle.", "Pull the {A}'s handle using {a}.", "<PERSON><PERSON><PERSON> {A}'s handle and pull to open.", "Pull the handle of {A} with {a}.", "Open {A} by pulling on its handle.", "Use {a} to grab and pull {A}.", "Pull the handle to open {A}.", "Grab and pull {A} using {a}.", "Pull {A}'s handle to open it.", "Use {a} to open {A} by pulling."], "unseen": ["Locate {A} and open it using {a}.", "Open {A} by pulling its door handle.", "Move {a} and open {A}.", "Open {A} using {a}.", "Use {a} to open {A}.", "Pull {A} open with {a}.", "Use {a} to pull open the microwave.", "Pull the microwave door open with {a}.", "<PERSON>rab {A}'s handle and pull to open it.", "Use {a} to pull the handle of {A}."]}