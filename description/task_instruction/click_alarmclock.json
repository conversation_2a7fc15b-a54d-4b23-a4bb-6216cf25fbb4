{"full_description": "click the alarm clock's center of the top side button on the table", "schema": "{A} notifies the alarm clock, {a} notifies the arm to click the alarm clock", "preference": "num of words should not exceed 10", "seen": ["{a} clicks the center top button on {A}", "Locate and press the top button on {A}", "Activate {A} by pressing the top button", "Press the center top button on {A} with {a}", "Use {a} to click the center button on {A}", "Click the top center button of {A}", "Press the button on {A}'s top side", "{a} presses the center top button on {A}", "Ensure {a} clicks the top center button of {A}", "{a} activates {A} by clicking the top button", "Tap the center button on {A}.", "Click {A}'s top center button using {a}.", "Touch {A}'s button on the top side.", "Point {a} to press the button of {A}.", "Click the button centered on {A}.", "Use {a} to tap {A}'s central button.", "Press the top button at {A}'s center.", "Guide {a} to press the centered button on {A}.", "Tap the button at {A}'s top center.", "Direct {a} to click the button found on {A}.", "Click the center of {A}'s top", "Use {a} to press {A} button", "Use {a} to click the top of {A}", "Tap {A}'s top button with {a}", "Push the top button on {A}", "Activate {A} by pressing its top", "Use {a} to activate {A}'s button", "Press the center top button of {A}", "Push the center area of {A}'s top", "Use {a} to press the center top of {A}", "Use {a} to press {A}'s top button", "Press the top middle button of {A}", "Touch {A}'s center top button with {a}", "Locate and press {A}'s top center button", "Activate {A} by clicking its top button", "Push the top button of {A} using {a}", "Click the middle button on {A}'s top side", "Use {a} to tap {A}'s top button center", "Hit the center of {A}'s top button", "Press {A}'s top button using {a}", "Use {a} to press {A}'s top center button.", "Push the button on {A}'s top center.", "Press the center button on {A}'s top.", "Use {a} to tap {A}'s top center button.", "Click the button centered on {A}'s top.", "Press the top button in {A}'s center.", "Use {a} to click the top center button on {A}.", "Push the central button on {A}'s top side.", "Use {a} to press the central button on {A}.", "Click the center button on {A}'s top side."], "unseen": ["Click the center top button of {A}", "Press the top button on {A}", "Press the top center of {A}.", "Use {a} to click {A}'s top center button.", "Click the top button of {A}", "Press {A}'s center top button", "Click the top button of {A}", "Tap the center top button of {A}", "Press {A}'s top center button.", "Click the central top button on {A}."]}