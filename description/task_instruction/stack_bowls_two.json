{"full_description": "stack the two bowls on top of each other", "schema": "{A} notifies the first bowl, {B} notifies the second bowl", "preference": "num of words should not exceed 15. Degree of detail avg 5", "seen": ["Lift {A}, put it on the table, and stack {B} above.", "Pick up {A}, place it down, then position {B} on top.", "Grab {A}, set it down, then place {B} over it.", "Place {A} on the surface, then stack {B} above it.", "Pick {A}, position on the table, and place {B} over it.", "Lift {A}, lay it down, and stack {B} on top.", "Take {A}, position it, then stack {B} directly above.", "Grab {A}, then carefully place {B} on top.", "Set {A} down, then add {B} over it.", "Lift {B}, stack it precisely on top of {A}.", "Pick {A}, then stack {B} on it.", "Grab {A}, position it, and place {B} on top.", "Align {A} and {B}, then stack {B} on {A}.", "Place {A} down, then set {B} on top of {A}.", "Position {A}, lift {B}, and place {B} on {A}.", "Pick up {A}, grab {B}, and stack {B} on {A}.", "Arrange {A}, lift {B} with the arm, and stack them.", "Use the arm to place {A}, then stack {B} on top.", "Position {A} with the arm, then set {B} above it.", "Grasp {A}, then use the arm to align and stack {B}.", "Grab {A}, then stack {B} on top.", "Lift {A}, position {B} above it, and stack.", "Set {B} over {A} to create the stack.", "Position {B} carefully on {A}.", "Hold {A} and stack {B} neatly atop.", "Take {A}, align {B}, and stack them.", "Place {A} down, then set {B} above it.", "Lift {B} and position it neatly over {A}.", "Stack {B} securely on top of {A}.", "Align {B} over {A}, then complete the stack.", "Place {A} down and stack {B} on top.", "Lift {A}, grab {B}, and set {B} on {A}.", "{A} goes at the bottom, {B} rests above.", "Pick {A}, position {B}, and place it over {A}.", "Put {A} first, then bring {B} to stack above.", "Stack {B} carefully over {A}.", "Set down {A} and drop {B} on top.", "Grab {A}, lift it up, position {B}, and stack them.", "Hold {A} steady and slide {B} on.", "Take {A}, place {B} above, and let go.", "Pick up {A} and set {B} on it.", "Take {A}, place it, and stack {B} on top.", "Lift {B}, then stack it on {A}.", "Position {A} and stack {B} securely onto it.", "Grab {A} with {a}, place it, and stack {B} with {b}.", "Pick up {A} using {a}, then stack {B} using {b}.", "With {a}, lift {A} and stack {B} using {b}.", "Use {a} for {A}, place it, and stack {B} with {b}.", "Grab {A} with {a}, put it down, then stack {B} onto it.", "Lift {A} using {a}, set it down, and stack {B} using {b}."], "unseen": ["Grab {A}, then place it on the surface.", "Take {A}, set it down, and stack {B} on top.", "Stack {B} on top of {A}.", "Place {B} carefully over {A} to stack them.", "Stack {B} directly over {A}.", "Place {B} onto {A} to form a stack.", "Grab {A}, then set {B} on top.", "Pick up {A} and stack {B} above.", "Grab {A} and stack {B} on it.", "Place {A} down, then put {B} on top."]}