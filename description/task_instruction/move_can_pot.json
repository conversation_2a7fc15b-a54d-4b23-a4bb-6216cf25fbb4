{"full_description": "there is a can and a pot on the table, use one arm to <pick up the can> and <move it to beside the pot>", "schema": "{A} notifies the pot, {B} notifies the can, {a} notifies the arm to grab the can", "preference": "num of words should not exceed 10", "seen": ["Use {a} to grab {B} and move it next to {A}", "Pick {B} up with {a} then place near {A}", "Move {B} from its spot to near {A}", "Lift {B} using {a} and drop it beside {A}", "Grab {B}, shift it, and place it close to {A}", "Take {B} with {a}, bring it, and set next to {A}", "Pick {B} up and carefully position it beside {A}", "Using {a}, lift {B} and place it by {A}", "Pick {B} with {a} and relocate it near {A}", "Lift {B} and move it near {A}", "Pick {B} up and move it next to {A}", "Grab {B} with {a} and set it near {A}", "Place {B} beside {A} after picking it up", "Use {a} to lift {B}, then move it next to {A}", "Move {B} beside {A} after lifting it", "Grab {B} with {a} and position it beside {A}", "Lift {B}, then place it next to {A}", "Use {a} to grab {B} and move it beside {A}", "Set {B} near {A} after picking it up", "Use {a} to lift {B} and place it near {A}", "Use {a} to take {B} to {A}", "Lift {B} and place it next to {A}", "Use {a} to move {B} beside {A}", "Pick up {B} with {a} and set by {A}", "Grab {B}, move it, and place by {A}", "Take {B} to {A} using {a}", "Set {B} right next to {A}", "With {a}, grab {B} and move to {A}", "Lift {B} and set it beside {A}", "Move {B} to {A} with {a}", "Lift {B} and set it next to {A}", "Use {a} to grab {B} and transfer it near {A}", "Pick up {B} and put it beside {A}", "Lift {B} using {a} and position it by {A}", "Move {B} to be next to {A}", "Use {a} to pick {B} up and set it beside {A}", "Place {B} next to {A}", "Grab {B} with {a} and move it close to {A}", "Bring {B} over and set it near {A}", "Use {a} to lift {B} and place it next to {A}", "Lift {B} and set it next to {A}", "Pick up {B} using {a}, transfer it beside {A}", "Grab {B}, move it to {A}'s side", "Take {B} with {a}, place it near {A}", "Pick {B} and position it next to {A}", "Use {a} to grab {B}, move it beside {A}", "Lift {B}, place it by {A}", "Take {B} using {a}, set it next to {A}", "Grab {B} and move it close to {A}", "Use {a} to pick {B}, position it near {A}"], "unseen": ["Pick up {B} and move it near {A}", "Grab {B} and set it beside {A}", "Lift {B} and set it beside {A}", "Use {a} to grab {B} and place it by {A}", "Pick up {B} and set it beside {A}", "Grab {B} and move it near {A}", "Grab {B} and place it near {A}", "Use {a} to pick up {B} and move it beside {A}", "Grab {B} and place it beside {A}", "Use {a} to pick up {B}, move it near {A}"]}