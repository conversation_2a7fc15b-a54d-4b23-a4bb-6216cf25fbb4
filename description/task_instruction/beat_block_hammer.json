{"full_description": "there is a hammer and a block on the table, use the arm to <grab the hammer> and <beat the block>", "schema": "{A} notifies the hammer, {a} notifies the arm to grab the hammer", "preference": "num of words should not exceed 10", "seen": ["Pick {A} and strike the block.", "Lift {A} using {a} to hit the block.", "Take {A} and smash the block.", "Employ {a} to grab {A} and hit.", "Hold {A} and pound the block.", "Utilize {a} to hold {A} and strike.", "Catch {A} and use it on the block.", "Grab {A} with {a} and beat the block.", "Grip {A} firmly and hit the block.", "Make {a} grab {A} and strike the block.", "Pick up {A} with {a}, strike the block.", "Lift {A}, then beat the block.", "Take {A} using {a}, hit the block.", "Grab {A}, then strike the block.", "With {a}, grab {A} and beat the block.", "Use {A} to hammer the block.", "With {a}, pick up {A} and strike the block.", "<PERSON>rab {A} to beat the block.", "Pick up {A} using {a}, hammer the block.", "Lift {A} and hit the block.", "With {a}, grab {A} and hit the block", "Pick up {A} and strike the block", "Grab {A} using {a}, then beat the block", "Take {A} and hammer the block", "Grab {A} with {a} and hit the block", "Pick up {A} and use it on the block", "With {a}, grab {A} and hammer the block", "Take {A} and beat the block", "Grab {A} using {a} and strike the block", "Lift {A} and hit the block", "Beat the block after grabbing {A}", "Grab {A} with {a} and strike block", "Hold {A} then hit the block", "Grab {A} using {a} then pound block", "Pick {A} and smash the block", "Lift {A} with {a} then strike block", "Grab {A}, then hit the block", "Use {a} to grab {A} and beat block", "Take {A} and strike the block", "Grab {A} with {a} and hit block", "Use {a} to grab {A} and beat", "Grab {A} using {a} and hit the block", "Take {A} and strike the block", "Pick up {A} using {a} then beat", "Use {A} to beat after grabbing with {a}", "Grab {A} and use it to hit", "Use {a} to take {A} and strike", "Pick {A} with {a} and hit the block", "Take {A} and beat the block", "Pick {A} from the table and strike"], "unseen": ["<PERSON>rab {A} and beat the block.", "Use {a} to pick up {A}.", "Use {a} to grab {A}, beat the block.", "Grab {A} and hit the block.", "Grab {A} and beat the block", "Use {A} to strike the block", "Grab {A} and hit the block", "Use {a} to grab {A} then beat block", "Grab {A} and strike the block", "Pick {A} up and hit the block"]}