from argparse import ArgumentParser
import json


def clear_seen_unseen(task_name):
    with open(f"./task_instruction/{task_name}.json", "r") as f:
        task_info_json = f.read()
    # print(task_info_json)
    task_info = json.loads(task_info_json)
    task_info["seen"] = []
    task_info["unseen"] = []
    with open(f"./task_instruction/{task_name}.json", "w") as f:
        json.dump(task_info, f, indent=2, ensure_ascii=False)


if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument("task_name", type=str, default="beat_block_hammer")
    args = parser.parse_args()
    clear_seen_unseen(args.task_name)
