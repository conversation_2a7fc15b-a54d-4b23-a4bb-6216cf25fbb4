import fcntl


class FileLock:
    """
    A file lock class.
    """

    def __init__(self, filename):
        self.filename = filename
        self.handle = None

    def acquire_read_lock(self):
        self.handle = open(self.filename + ".lock", "r")
        fcntl.flock(self.handle, fcntl.LOCK_SH | fcntl.LOCK_NB)

    def acquire_write_lock(self):
        self.handle = open(self.filename + ".lock", "w")
        fcntl.flock(self.handle, fcntl.LOCK_EX | fcntl.LOCK_NB)

    def release_lock(self):
        if self.handle is not None:
            fcntl.flock(self.handle, fcntl.LOCK_UN)
            self.handle.close()
            self.handle = None
