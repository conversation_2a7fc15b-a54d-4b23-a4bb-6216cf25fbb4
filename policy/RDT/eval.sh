#!/bin/bash

policy_name=RDT
task_name=${1}
task_config=${2}
model_name=${3}
checkpoint_id=${4}
seed=${5}
gpu_id=${6}

DEBUG=False
export CUDA_VISIBLE_DEVICES=${gpu_id}
echo -e "\033[33mgpu id (to use): ${gpu_id}\033[0m"

cd ../.. # move to root

PYTHONWARNINGS=ignore::UserWarning \
python script/eval_policy.py --config policy/$policy_name/deploy_policy.yml \
    --overrides \
    --task_name ${task_name} \
    --task_config ${task_config} \
    --ckpt_setting ${model_name} \
    --seed ${seed} \
    --checkpoint_id ${checkpoint_id} \
    --policy_name ${policy_name}
