import sys
import yaml


def read_yaml_value(file_path, key):
    with open(file_path, "r") as file:
        data = yaml.safe_load(file)
        value = data.get(key)
        if value is not None:
            print(value)
        else:
            print(f"Key '{key}' not found in {file_path}")


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python read_yaml.py <file_path> <key>")
        sys.exit(1)

    file_path = sys.argv[1]
    key = sys.argv[2]
    read_yaml_value(file_path, key)
