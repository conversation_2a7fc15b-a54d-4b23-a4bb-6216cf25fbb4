#!/bin/bash

tasks=(
#"adjust_bottle"
#"beat_block_hammer"
#"blocks_ranking_rgb"
#"blocks_ranking_size"
#"click_alarmclock"
#"click_bell"
#"dump_bin_bigbin"
#"grab_roller"
#"handover_block"
#"handover_mic"

#"hanging_mug"
#"lift_pot"
#"move_can_pot"
#"move_pillbottle_pad"
#"move_playingcard_away"
#"move_stapler_pad"
#"open_laptop"
#"open_microwave"
#"pick_dual_bottles"
#"pick_diverse_bottles"


#"place_a2b_left"
#"place_a2b_right"
#"place_bread_basket"
#"place_bread_skillet"
#"place_can_basket"
#"place_cans_plasticbox"
#"place_container_plate"
#"place_dual_shoes"
#"place_empty_cup"
#"place_fan"

#"place_burger_fries"
#"place_mouse_pad"
#"place_object_basket"
#"place_object_scale"
#"place_object_stand"
#"place_phone_stand"
#"place_shoe"
#"press_stapler"
#"put_bottles_dustbin"
#"put_object_cabinet"

#"rotate_qrcode"
#"shake_bottle"
#"shake_bottle_horizontally"
#"stack_blocks_three"
#"stack_blocks_two"
#"stack_bowls_three"
#"stack_bowls_two"
#"stamp_seal"
#"turn_switch"
)

# directory paths
SAVE_ROOT_DIR="/yourpath/RoboTwin/policy/LLaVA-VLA/pictures"
PYTHON_SCRIPT="/yourpath/RoboTwin/policy/LLaVA-VLA/llava/process_data/image_extraction.py"

# check if the script is run with sufficient arguments
if [ $# -lt 1 ]; then
    echo "Usage: $0 <task_name> <task_config> | $0 --all <task_config>"
    exit 1
fi

# process command line arguments
if [ "$1" == "all" ]; then
    if [ $# -ne 2 ]; then
        echo "Usage for all tasks: $0 all <task_config>"
        exit 1
    fi
    task_config="$2"

    echo "📦 Found ${#tasks[@]} tasks: ${tasks[*]}"

    for task in "${tasks[@]}"; do
        DATA_ROOT_DIR="/yourpath/RoboTwin/data/$task/$task_config/data/"
        echo "Starting task: $task"
        python "$PYTHON_SCRIPT" \
            --task_name "$task" \
            --task_config "$task_config" \
            --data_root_dir "$DATA_ROOT_DIR" \
            --save_root_dir "$SAVE_ROOT_DIR"
        if [ $? -eq 0 ]; then
            echo "Task $task completed successfully"
        else
            echo " Task $task failed"
        fi
        echo "----------------------------------------"
    done

    echo " All tasks completed!"
else
    # 单任务模式
    TASK_NAME="$1"
    task_config="$2"
    DATA_ROOT_DIR="/yourpath/RoboTwin/data/$TASK_NAME/$task_config/data/"

    echo "Start processing task: $TASK_NAME"
    python "$PYTHON_SCRIPT" \
        --task_name "$TASK_NAME" \
        --task_config "$task_config" \
        --data_root_dir "$DATA_ROOT_DIR" \
        --save_root_dir "$SAVE_ROOT_DIR"
    if [ $? -eq 0 ]; then
        echo " Task $TASK_NAME completed successfully"
    else
        echo " Task $TASK_NAME failed"
    fi
fi