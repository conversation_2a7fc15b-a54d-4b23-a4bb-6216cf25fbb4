#!/bin/bash

export WANDB_MODE=offline
export WANDB_DIR=./wandb
export MODEL_NAME_OR_PATH=/yourpath/model_zoo/llava-v1.5-7b
export OUTPUT_DIR=./checkpoints
export JSON_PATH=/yourpath/RoboTwin/policy/LLaVA-VLA/training_data/data.json
export pictures_DIRECTORY=/yourpath/RoboTwin/policy/LLaVA-VLA/pictures
export ACTION_STAT=/yourpath/RoboTwin/policy/LLaVA-VLA/yaml_statistics/statistics.yaml
export VISION_TOWER=/yourpath/model_zoo/clip-vit-large-patch14-336
export DEEPSPEED_CONFIG=./LLaVA-VLA/scripts/zero3.json

# Please replace 'yourpath' with your actual path!
# Specify GPU IDs after localhost (e.g., 0,1 for two GPUs)
deepspeed --include=localhost:0,1 ./LLaVA-VLA/llava/train/calvin_train_obs.py \
    --deepspeed $DEEPSPEED_CONFIG \
    --model_name_or_path $MODEL_NAME_OR_PATH \
    --version v1 \
    --data_path $JSON_PATH \
    --image_folder $pictures_DIRECTORY \
    --action_stat $ACTION_STAT \
    --vision_tower $VISION_TOWER \
    --mm_projector_type mlp2x_gelu \
    --mm_vision_select_layer -2 \
    --mm_use_im_start_end False \
    --mm_use_im_patch_token False \
    --image_aspect_ratio pad \
    --group_by_modality_length False \
    --bf16 True \
    --output_dir $OUTPUT_DIR \
    --num_train_epochs 1 \
    --per_device_train_batch_size 32 \
    --per_device_eval_batch_size 4 \
    --gradient_accumulation_steps 1 \
    --evaluation_strategy no \
    --save_strategy epoch \
    --save_total_limit 1 \
    --learning_rate 2e-5 \
    --weight_decay 0. \
    --warmup_ratio 0.03 \
    --lr_scheduler_type cosine \
    --logging_steps 1 \
    --tf32 True \
    --model_max_length 2048 \
    --gradient_checkpointing True \
    --dataloader_num_workers 4 \
    --lazy_preprocess True \
    --report_to wandb \
    --report_to_wandb_project your_project_name \
    --report_to_wandb_run_name your_run_name
