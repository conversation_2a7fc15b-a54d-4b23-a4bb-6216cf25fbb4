import os
from .clip_encoder import CLIPVisionTower
from .whisper_encoder import WhisperAudioTower


def build_vision_tower(vision_tower_cfg, **kwargs):
    vision_tower = getattr(vision_tower_cfg, 'mm_vision_tower', getattr(vision_tower_cfg, 'vision_tower', None))
    # print("vision_tower_cfg:",vision_tower_cfg)
    # print("vision_tower:",vision_tower)
    # vision_tower="/data/user/wsong890/user68/project/clip-vit-large-patch14-336"
    is_absolute_path_exists = os.path.exists(vision_tower)
    # print("is_absolute_path_exists:",is_absolute_path_exists)
    if is_absolute_path_exists or vision_tower.startswith("openai") or vision_tower.startswith("laion") or "ShareGPT4V" in vision_tower:
        return CLIPVisionTower(vision_tower, args=vision_tower_cfg, **kwargs)

    raise ValueError(f'Unknown vision tower: {vision_tower}')

def build_audio_tower(audio_tower_cfg, **kwargs):
    audio_tower = getattr(audio_tower_cfg, 'mm_audio_tower', getattr(audio_tower_cfg, 'audio_tower', None))
    is_absolute_path_exists = os.path.exists(audio_tower)
    if is_absolute_path_exists or audio_tower.startswith("openai"):
        return WhisperAudioTower(audio_tower, args=audio_tower_cfg, **kwargs)
    raise ValueError(f'Unknown audio tower: {audio_tower}')