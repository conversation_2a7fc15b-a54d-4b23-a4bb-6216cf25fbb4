import torch
import torch.nn as nn

from transformers import WhisperProcessor, WhisperForConditionalGeneration, WhisperConfig


class WhisperAudioTower(nn.Module):
    def __init__(self, audio_tower, args, delay_load=False):
        super().__init__()
        self.is_loaded = False
        self.audio_tower_name = audio_tower
        
        if not delay_load:
            self.load_model()
        elif getattr(args, "unfreeze_mm_audio_tower", False):
            self.load_model()
        else:
            self.cfg_only = WhisperConfig.from_pretrained(self.audio_tower_name)

    def load_model(self, device_map=None):
        if self.is_loaded:
            print('{} is already loaded, `load_model` called again, skipping.'.format(self.audio_tower_name))
            return

        self.audio_processor = WhisperProcessor.from_pretrained(self.audio_tower_name)
        self.audio_tower = WhisperForConditionalGeneration.from_pretrained(self.audio_tower_name, device_map=device_map)
        self.audio_tower = self.audio_tower.model.encoder
        self.audio_tower.requires_grad_(False)
        self.is_loaded = True

    def feature_select(self, audio_forward_outs):
        audio_features = audio_forward_outs.hidden_states[-1]
        return audio_features

    @torch.no_grad()
    def forward(self, audios):
        audio_forward_outs = self.audio_tower(audios.to(device=self.device, dtype=self.dtype), output_hidden_states=True)
        audio_features = self.feature_select(audio_forward_outs).to(audios.dtype)
        return audio_features

    @property
    def dtype(self):
        return self.audio_tower.dtype

    @property
    def device(self):
        return self.audio_tower.device

    @property
    def config(self):
        if self.is_loaded:
            return self.audio_tower.config
        else:
            return self.cfg_only

    @property
    def hidden_size(self):
        return self.config.hidden_size
