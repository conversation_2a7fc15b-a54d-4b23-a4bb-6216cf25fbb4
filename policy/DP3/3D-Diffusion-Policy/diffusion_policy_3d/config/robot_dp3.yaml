defaults:
  - task: demo_task

name: dp3

task_name: null
shape_meta: ${task.shape_meta}
exp_name: "debug"

horizon: 8
n_obs_steps: 3
n_action_steps: 6
n_latency_steps: 0
dataset_obs_steps: ${n_obs_steps}
keypoint_visible_rate: 1.0
obs_as_global_cond: True

policy:
  _target_: diffusion_policy_3d.policy.dp3.DP3
  use_point_crop: true
  condition_type: film
  use_down_condition: true
  use_mid_condition: true
  use_up_condition: true
  
  diffusion_step_embed_dim: 64
  down_dims:
  - 512
  - 1024
  - 2048
  crop_shape:
  - 80
  - 80
  encoder_output_dim: 128 # dual 128, raw 64
  horizon: ${horizon}
  kernel_size: 5
  n_action_steps: ${n_action_steps}
  n_groups: 8
  n_obs_steps: ${n_obs_steps}

  noise_scheduler:
    _target_: diffusers.schedulers.scheduling_ddim.DDIMScheduler
    num_train_timesteps: 100
    beta_start: 0.0001
    beta_end: 0.02
    beta_schedule: squaredcos_cap_v2
    clip_sample: True
    set_alpha_to_one: True
    steps_offset: 0
    prediction_type: sample


  num_inference_steps: 10
  obs_as_global_cond: true
  shape_meta: ${shape_meta}

  use_pc_color: false
  pointnet_type: "pointnet"


  pointcloud_encoder_cfg:
    in_channels: 3
    out_channels: ${policy.encoder_output_dim}
    use_layernorm: true
    final_norm: layernorm # layernorm, none
    normal_channel: false


ema:
  _target_: diffusion_policy_3d.model.diffusion.ema_model.EMAModel
  update_after_step: 0
  inv_gamma: 1.0
  power: 0.75
  min_value: 0.0
  max_value: 0.9999

dataloader:
  batch_size: 256
  num_workers: 8
  shuffle: True
  pin_memory: True
  persistent_workers: False

val_dataloader:
  batch_size: 256
  num_workers: 8
  shuffle: False
  pin_memory: True
  persistent_workers: False

optimizer:
  _target_: torch.optim.AdamW
  lr: 1.0e-4
  betas: [0.95, 0.999]
  eps: 1.0e-8
  weight_decay: 1.0e-6

training:
  device: "cuda:0"
  seed: 42
  debug: False
  resume: True
  lr_scheduler: cosine
  lr_warmup_steps: 500
  num_epochs: 3000
  gradient_accumulate_every: 1
  use_ema: True
  rollout_every: 200
  checkpoint_every: 3000
  val_every: 50
  sample_every: 20
  max_train_steps: null
  max_val_steps: null
  tqdm_interval_sec: 1.0

logging:
  group: ${exp_name}
  id: null
  mode: online
  name: ${exp_name}
  project: RoboTwin
  resume: true
  tags:
  - RoboTwin

checkpoint:
  save_ckpt: False # if True, save checkpoint every checkpoint_every
  topk:
    monitor_key: test_mean_score
    mode: max
    k: 1
    format_str: 'epoch={epoch:04d}-test_mean_score={test_mean_score:.3f}.ckpt'
  save_last_ckpt: True # this only saves when save_ckpt is True
  save_last_snapshot: False
  
hydra:
  job:
    override_dirname: ${name}
  run:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  sweep:
    dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
    subdir: ${hydra.job.num}

multi_run:
  run_dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  wandb_name_base: ${now:%Y.%m.%d-%H.%M.%S}_${name}_${task_name}

checkpoint_num: 3000
expert_data_num: 100
raw_task_name: none
setting: none
