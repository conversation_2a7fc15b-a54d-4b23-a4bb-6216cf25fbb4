# Basic experiment configuration (keep unchanged)
policy_name: DexVLA
task_name: task_name_you_test
task_config: null
ckpt_setting: null
seed: null
instruction_type: unseen

# Specify path to trained DexVLA(Required)
model_path: "/Modify/policy/DexVLA/model/150000"
stats_path: "/Modify/robo2/policy/DexVLA/model/dataset_stats.pkl"
action_head: 'scale_dp_policy' # "dit_diffusion_policy"   # or 'unet_diffusion_policy'
setting: NULL
