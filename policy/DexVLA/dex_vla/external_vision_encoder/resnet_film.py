from typing import Type, Any, Callable, Union, List, Mapping, Optional

import copy
import torch
import torch.nn as nn
from torch import Tensor


def is_torch_version_lower_than_17():
    major_version = float(torch.__version__.split('.')[0])
    minor_version = float(torch.__version__.split('.')[1])
    return major_version == 1 and minor_version < 7


if not is_torch_version_lower_than_17():
    # TODO: Make sure the torchvision version is similarly updated.
    from torchvision.models import ResNet18_Weights, ResNet34_Weights, ResNet101_Weights, ResNet50_Weights


def conv3x3(in_planes: int, out_planes: int, stride: int = 1, groups: int = 1, dilation: int = 1) -> nn.Conv2d:
    """3x3 convolution with padding"""
    return nn.Conv2d(
        in_planes,
        out_planes,
        kernel_size=3,
        stride=stride,
        padding=dilation,
        groups=groups,
        bias=False,
        dilation=dilation,
    )


def conv1x1(in_planes: int, out_planes: int, stride: int = 1) -> nn.Conv2d:
    """1x1 convolution"""
    return nn.Conv2d(in_planes, out_planes, kernel_size=1, stride=stride, bias=False)


class BasicBlock(nn.Module):
    expansion: int = 1

    def __init__(
            self,
            inplanes: int,
            planes: int,
            stride: int = 1,
            downsample: Optional[nn.Module] = None,
            groups: int = 1,
            base_width: int = 64,
            dilation: int = 1,
            norm_layer: Optional[Callable[..., nn.Module]] = None,
    ) -> None:
        super().__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        if groups != 1 or base_width != 64:
            raise ValueError("BasicBlock only supports groups=1 and base_width=64")
        if dilation > 1:
            raise NotImplementedError("Dilation > 1 not supported in BasicBlock")
        # Both self.conv1 and self.downsample layers downsample the input when stride != 1
        self.conv1 = conv3x3(inplanes, planes, stride)
        self.bn1 = norm_layer(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = conv3x3(planes, planes)
        self.bn2 = norm_layer(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x: Tensor, film_features: Optional[Tensor] = None) -> Tensor:
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        # Apply FiLM here
        if film_features is not None:
            # gamma, beta will be (B, 1, 1, planes)
            gamma, beta = torch.split(film_features, 1, dim=1)
            gamma = gamma.squeeze().view(x.size(0), -1, 1, 1)
            beta = beta.squeeze().view(x.size(0), -1, 1, 1)
            out = (1 + gamma) * out + beta

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class Bottleneck(nn.Module):
    # Bottleneck in torchvision places the stride for downsampling at 3x3 convolution(self.conv2)
    # while original implementation places the stride at the first 1x1 convolution(self.conv1)
    # according to "Deep residual learning for image recognition"https://arxiv.org/abs/1512.03385.
    # This variant is also known as ResNet V1.5 and improves accuracy according to
    # https://ngc.nvidia.com/catalog/model-scripts/nvidia:resnet_50_v1_5_for_pytorch.

    expansion: int = 4

    def __init__(
            self,
            inplanes: int,
            planes: int,
            stride: int = 1,
            downsample: Optional[nn.Module] = None,
            groups: int = 1,
            base_width: int = 64,
            dilation: int = 1,
            norm_layer: Optional[Callable[..., nn.Module]] = None, ) -> None:
        super().__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        width = int(planes * (base_width / 64.0)) * groups
        # Both self.conv2 and self.downsample layers downsample the input when stride != 1
        self.conv1 = conv1x1(inplanes, width)
        self.bn1 = norm_layer(width)
        self.conv2 = conv3x3(width, width, stride, groups, dilation)
        self.bn2 = norm_layer(width)
        self.conv3 = conv1x1(width, planes * self.expansion)
        self.bn3 = norm_layer(planes * self.expansion)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x: Tensor) -> Tensor:
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity

        out = self.relu(out)

        return out


class ResNetWithExtraModules(nn.Module):
    """Update standard ResNet image classification models with FiLM."""

    def __init__(
            self,
            block: Type[Union[BasicBlock, Bottleneck]],
            layers: List[int],
            num_classes: int = 1000,
            zero_init_residual: bool = False,
            groups: int = 1,
            width_per_group: int = 64,
            replace_stride_with_dilation: Optional[List[bool]] = None,
            norm_layer: Optional[Callable[..., nn.Module]] = None,
            film_config: Optional[Mapping[str, Any]] = None, ) -> None:
        super().__init__()
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d
        self._norm_layer = norm_layer

        # Save how many blocks in each layer
        self.layers = layers

        # FiLM only implemented for BasicBlock for now
        self.use_film = film_config is not None and film_config['use']
        if self.use_film:
            self.film_config = film_config
            self.film_planes = film_config['film_planes']
            self.expansion = block.expansion

        self.inplanes = 64
        self.dilation = 1
        if replace_stride_with_dilation is None:
            # each element in the tuple indicates if we should replace
            # the 2x2 stride with a dilated convolution instead
            replace_stride_with_dilation = [False, False, False]
        if len(replace_stride_with_dilation) != 3:
            raise ValueError(
                "replace_stride_with_dilation should be None "
                f"or a 3-element tuple, got {replace_stride_with_dilation}"
            )

        in_channels_conv1 = 4 if (
                film_config is not None and
                film_config.get('append_object_mask', None) is not None) else 3

        self.groups = groups
        self.base_width = width_per_group
        self.conv1 = nn.Conv2d(in_channels_conv1, self.inplanes, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = norm_layer(self.inplanes)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        self.layer1 = self._make_layer(block, 256, layers[0])
        self.layer2 = self._make_layer(block, 512, layers[1], stride=2, dilate=replace_stride_with_dilation[0])
        self.layer3 = self._make_layer(block, 1024, layers[2], stride=2, dilate=replace_stride_with_dilation[1])
        self.layer4 = self._make_layer(block, 2048, layers[3], stride=2, dilate=replace_stride_with_dilation[2])
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(512 * block.expansion, num_classes)

        for m_name, m in self.named_modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode="fan_out", nonlinearity="relu")
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

        # Zero-initialize the last BN in each residual branch,
        # so that the residual branch starts with zeros, and each residual block behaves like an identity.
        # This improves the model by 0.2~0.3% according to https://arxiv.org/abs/1706.02677
        if zero_init_residual:
            for m in self.modules():
                if isinstance(m, Bottleneck) and m.bn3.weight is not None:
                    nn.init.constant_(m.bn3.weight, 0)  # type: ignore[arg-type]
                elif isinstance(m, BasicBlock) and m.bn2.weight is not None:
                    nn.init.constant_(m.bn2.weight, 0)  # type: ignore[arg-type]

    def _make_layer(
            self,
            block: Type[Union[BasicBlock, Bottleneck]],
            planes: int,
            blocks: int,
            stride: int = 1,
            dilate: bool = False, ) -> nn.Sequential:
        norm_layer = self._norm_layer
        downsample = None
        previous_dilation = self.dilation
        if dilate:
            self.dilation *= stride
            stride = 1
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                conv1x1(self.inplanes, planes * block.expansion, stride),
                norm_layer(planes * block.expansion),
            )

        layers = [
            block(self.inplanes, planes, stride, downsample, self.groups, self.base_width, previous_dilation,
                  norm_layer, )
        ]
        self.inplanes = planes * block.expansion
        for _ in range(1, blocks):
            layers.append(
                block(
                    self.inplanes,
                    planes,
                    groups=self.groups,
                    base_width=self.base_width,
                    dilation=self.dilation,
                    norm_layer=norm_layer,
                )
            )

        if self.use_film:
            return nn.ModuleList(layers)
        else:
            return nn.Sequential(*layers)

    def _forward_impl_film(self, x: Tensor, film_features: List[Optional[Tensor]], flatten: bool = True):
        assert self.use_film and film_features is not None

        def _extract_film_features_for_layer(film_feat: Optional[Tensor], layer_idx: int):
            if film_features[layer_idx] is None:
                return [None] * self.layers[layer_idx]

            num_planes = self.film_planes[layer_idx]
            num_blocks = self.layers[layer_idx]
            film_feat = film_feat.view(-1, 2, num_blocks, num_planes)
            film_feat_per_block = torch.split(film_feat, 1, dim=2)
            return film_feat_per_block

        for layer_idx, layer in enumerate([self.layer1, self.layer2, self.layer3, self.layer4]):
            film_feat_per_block = _extract_film_features_for_layer(
                film_features[layer_idx], layer_idx)
            for block_idx, block in enumerate(layer):
                if film_feat_per_block[block_idx] is not None:
                    assert x.shape[0] == film_feat_per_block[block_idx].shape[0], ('FiLM batch size does not match')
                x = block(x, film_features=film_feat_per_block[block_idx])

        x = self.avgpool(x)
        if flatten:
            x = torch.flatten(x, 1)
        x = self.fc(x)
        return x

    def _forward_impl(self,
                      x: Tensor,
                      film_features: List[Optional[Tensor]],
                      flatten: bool = True) -> Tensor:
        # See note [TorchScript super()]
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        if self.use_film:
            return self._forward_impl_film(x, film_features, flatten=flatten)
        else:
            x = self.layer1(x)
            x = self.layer2(x)
            x = self.layer3(x)
            x = self.layer4(x)

            x = self.avgpool(x)
            if flatten:
                x = torch.flatten(x, 1)
            x = self.fc(x)

            return x

    def forward(self,
                x: Tensor,
                film_features: List[Optional[Tensor]], **kwargs) -> Tensor:
        return self._forward_impl(x, film_features, **kwargs)


def _resnet(
        block: Type[Union[BasicBlock, Bottleneck]],
        layers: List[int],
        weights,
        progress: bool,
        **kwargs: Any,
) -> ResNetWithExtraModules:
    model_kwargs = copy.deepcopy(kwargs)
    if 'pretrained' in model_kwargs:
        del model_kwargs['pretrained']
    if 'arch' in model_kwargs:
        del model_kwargs['arch']
    model = ResNetWithExtraModules(block, layers, **model_kwargs)

    if weights is not None:
        model.load_state_dict(weights.get_state_dict(progress=progress))
    elif kwargs.get('pretrained', False) and kwargs.get('arch') is not None:
        if float(torch.__version__.split('.')[1]) < 7:
            # Copied from https://pytorch.org/vision/0.11/_modules/torchvision/models/resnet.html#resnet18
            model_urls = {
                'resnet18': 'https://download.pytorch.org/models/resnet18-f37072fd.pth',
                'resnet34': 'https://download.pytorch.org/models/resnet34-b627a593.pth',
                'resnet50': 'https://download.pytorch.org/models/resnet50-0676ba61.pth',
                'resnet101': 'https://download.pytorch.org/models/resnet101-63fe2227.pth',
                'resnet152': 'https://download.pytorch.org/models/resnet152-394f9c45.pth',
                'resnext50_32x4d': 'https://download.pytorch.org/models/resnext50_32x4d-7cdf4587.pth',
                'resnext101_32x8d': 'https://download.pytorch.org/models/resnext101_32x8d-8ba56ff5.pth',
                'wide_resnet50_2': 'https://download.pytorch.org/models/wide_resnet50_2-95faca4d.pth',
                'wide_resnet101_2': 'https://download.pytorch.org/models/wide_resnet101_2-32ee1156.pth',
            }

            # state_dict = load_state_dict_from_url(model_urls[arch],
            #                                     progress=progress)
            state_dict = torch.hub.load_state_dict_from_url(model_urls[kwargs.get('arch')],
                                                            progress=progress)
            model.load_state_dict(state_dict)

    return model


def resnet18(*, weights=None, progress: bool = True, **kwargs: Any) -> ResNetWithExtraModules:
    """ResNet-18 from `Deep Residual Learning for Image Recognition <https://arxiv.org/pdf/1512.03385.pdf>`__.

    Args:
        weights (:class:`~torchvision.models.ResNet18_Weights`, optional): The
            pretrained weights to use. See
            :class:`~torchvision.models.ResNet18_Weights` below for
            more details, and possible values. By default, no pre-trained
            weights are used.
        progress (bool, optional): If True, displays a progress bar of the
            download to stderr. Default is True.
        **kwargs: parameters passed to the ``torchvision.models.resnet.ResNet``
            base class. Please refer to the `source code
            <https://github.com/pytorch/vision/blob/main/torchvision/models/resnet.py>`_
            for more details about this class.

    .. autoclass:: torchvision.models.ResNet18_Weights
        :members:
    """
    if is_torch_version_lower_than_17():
        kwargs["arch"] = "resnet18"
        weights = None
    else:
        weights = ResNet18_Weights.verify(weights)

    return _resnet(BasicBlock, [2, 2, 2, 2], weights, progress, **kwargs)


def resnet34(*, weights=None, progress: bool = True, **kwargs: Any) -> ResNetWithExtraModules:
    """ResNet-34 from `Deep Residual Learning for Image Recognition <https://arxiv.org/pdf/1512.03385.pdf>`__.

    Args:
        weights (:class:`~torchvision.models.ResNet34_Weights`, optional): The
            pretrained weights to use. See
            :class:`~torchvision.models.ResNet34_Weights` below for
            more details, and possible values. By default, no pre-trained
            weights are used.
        progress (bool, optional): If True, displays a progress bar of the
            download to stderr. Default is True.
        **kwargs: parameters passed to the ``torchvision.models.resnet.ResNet``
            base class. Please refer to the `source code
            <https://github.com/pytorch/vision/blob/main/torchvision/models/resnet.py>`_
            for more details about this class.

    .. autoclass:: torchvision.models.ResNet34_Weights
        :members:
    """
    if is_torch_version_lower_than_17():
        kwargs["arch"] = "resnet34"
        weights = None
    else:
        weights = ResNet34_Weights.verify(weights)

    return _resnet(BasicBlock, [3, 4, 6, 3], weights, progress, **kwargs)


def resnet50(*, weights=None, progress: bool = True, **kwargs: Any) -> ResNetWithExtraModules:
    """Res 50 from `Deep Residual Learning for Image Recognition <https://arxiv.org/pdf/1512.03385.pdf>`__."""
    if is_torch_version_lower_than_17():
        kwargs["arch"] = "resnet50"
        weights = None
    else:
        weights = ResNet50_Weights.verify(weights)
    return _resnet(BasicBlock, [3, 4, 6, 3], weights, progress, **kwargs)


def resnet101(*, weights=None, progress: bool = True, **kwargs: Any) -> ResNetWithExtraModules:
    """ResNet-101 from `Deep Residual Learning for Image Recognition <https://arxiv.org/pdf/1512.03385.pdf>`__.

    .. note::
       The bottleneck of TorchVision places the stride for downsampling to the second 3x3
       convolution while the original paper places it to the first 1x1 convolution.
       This variant improves the accuracy and is known as `ResNet V1.5
       <https://ngc.nvidia.com/catalog/model-scripts/nvidia:resnet_50_v1_5_for_pytorch>`_.

    Args:
        weights (:class:`~torchvision.models.ResNet101_Weights`, optional): The
            pretrained weights to use. See
            :class:`~torchvision.models.ResNet101_Weights` below for
            more details, and possible values. By default, no pre-trained
            weights are used.
        progress (bool, optional): If True, displays a progress bar of the
            download to stderr. Default is True.
        **kwargs: parameters passed to the ``torchvision.models.resnet.ResNet``
            base class. Please refer to the `source code
            <https://github.com/pytorch/vision/blob/main/torchvision/models/resnet.py>`_
            for more details about this class.

    .. autoclass:: torchvision.models.ResNet101_Weights
        :members:
    """
    if is_torch_version_lower_than_17():
        kwargs["arch"] = "resnet101"
        weights = None
    else:
        weights = ResNet101_Weights.verify(weights)
    return _resnet(Bottleneck, [3, 4, 23, 3], weights, progress, **kwargs)