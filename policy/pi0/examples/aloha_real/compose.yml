# Run with:
# docker compose -f examples/aloha_real/compose.yml up --build
services:
  runtime:
    image: aloha_real
    depends_on:
      - aloha_ros_nodes
      - ros_master
      - openpi_server
    build:
      context: ../..
      dockerfile: examples/aloha_real/Dockerfile
    init: true
    tty: true
    network_mode: host
    privileged: true
    volumes:
      - $PWD:/app
      - ../../data:/data

  aloha_ros_nodes:
    image: aloha_real
    depends_on:
      - ros_master
    build:
      context: ../..
      dockerfile: examples/aloha_real/Dockerfile
    init: true
    tty: true
    network_mode: host
    privileged: true
    volumes:
      - /dev:/dev
    command: roslaunch --wait aloha ros_nodes.launch

  ros_master:
    image: ros:noetic-robot
    network_mode: host
    privileged: true
    command:
      - roscore

  openpi_server:
    image: openpi_server
    build:
      context: ../..
      dockerfile: scripts/docker/serve_policy.Dockerfile
    init: true
    tty: true
    network_mode: host
    volumes:
      - $PWD:/app
      - ${OPENPI_DATA_HOME:-~/.cache/openpi}:/openpi_assets
    environment:
      - SERVER_ARGS
      - OPENPI_DATA_HOME=/openpi_assets
      - IS_DOCKER=true

    # Comment out this block if not running on a machine with GPUs.
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
