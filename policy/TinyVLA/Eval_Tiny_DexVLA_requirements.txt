accelerate==0.26.0
adal==1.2.7
addict==2.4.0
annotated-types==0.7.0
anyio==4.9.0
asciitree==0.3.3
asttokens==3.0.0
attrs==25.3.0
av==14.4.0
azure==4.0.0
azure-ai-inference==1.0.0b9
azure-applicationinsights==0.1.1
azure-batch==4.1.3
azure-common==1.1.28
azure-core==1.34.0
azure-cosmosdb-nspkg==2.0.2
azure-cosmosdb-table==1.0.6
azure-datalake-store==0.0.53
azure-eventgrid==1.3.0
azure-graphrbac==0.40.0
azure-keyvault==1.1.0
azure-loganalytics==0.1.1
azure-mgmt==4.0.0
azure-mgmt-advisor==1.0.1
azure-mgmt-applicationinsights==0.1.1
azure-mgmt-authorization==0.50.0
azure-mgmt-batch==5.0.1
azure-mgmt-batchai==2.0.0
azure-mgmt-billing==0.2.0
azure-mgmt-cdn==3.1.0
azure-mgmt-cognitiveservices==3.0.0
azure-mgmt-commerce==1.0.1
azure-mgmt-compute==4.6.2
azure-mgmt-consumption==2.0.0
azure-mgmt-containerinstance==1.5.0
azure-mgmt-containerregistry==2.8.0
azure-mgmt-containerservice==4.4.0
azure-mgmt-cosmosdb==0.4.1
azure-mgmt-datafactory==0.6.0
azure-mgmt-datalake-analytics==0.6.0
azure-mgmt-datalake-nspkg==3.0.1
azure-mgmt-datalake-store==0.5.0
azure-mgmt-datamigration==1.0.0
azure-mgmt-devspaces==0.1.0
azure-mgmt-devtestlabs==2.2.0
azure-mgmt-dns==2.1.0
azure-mgmt-eventgrid==1.0.0
azure-mgmt-eventhub==2.6.0
azure-mgmt-hanaonazure==0.1.1
azure-mgmt-iotcentral==0.1.0
azure-mgmt-iothub==0.5.0
azure-mgmt-iothubprovisioningservices==0.2.0
azure-mgmt-keyvault==1.1.0
azure-mgmt-loganalytics==0.2.0
azure-mgmt-logic==3.0.0
azure-mgmt-machinelearningcompute==0.4.1
azure-mgmt-managementgroups==0.1.0
azure-mgmt-managementpartner==0.1.1
azure-mgmt-maps==0.1.0
azure-mgmt-marketplaceordering==0.1.0
azure-mgmt-media==1.0.1
azure-mgmt-monitor==0.5.2
azure-mgmt-msi==0.2.0
azure-mgmt-network==2.7.0
azure-mgmt-notificationhubs==2.1.0
azure-mgmt-nspkg==3.0.2
azure-mgmt-policyinsights==0.1.0
azure-mgmt-powerbiembedded==2.0.0
azure-mgmt-rdbms==1.9.0
azure-mgmt-recoveryservices==0.3.0
azure-mgmt-recoveryservicesbackup==0.3.0
azure-mgmt-redis==5.0.0
azure-mgmt-relay==0.1.0
azure-mgmt-reservations==0.2.1
azure-mgmt-resource==2.2.0
azure-mgmt-scheduler==2.0.0
azure-mgmt-search==2.1.0
azure-mgmt-servicebus==0.5.3
azure-mgmt-servicefabric==0.2.0
azure-mgmt-signalr==0.1.1
azure-mgmt-sql==0.9.1
azure-mgmt-storage==2.0.0
azure-mgmt-subscription==0.2.0
azure-mgmt-trafficmanager==0.50.0
azure-mgmt-web==0.35.0
azure-nspkg==3.0.2
azure-servicebus==0.21.1
azure-servicefabric==*******
azure-servicemanagement-legacy==0.20.8
azure-storage-blob==1.5.0
azure-storage-common==1.4.2
azure-storage-file==1.4.0
azure-storage-queue==1.4.0
blinker==1.9.0
brotlicffi @ file:///croot/brotlicffi_1736182461069/work
certifi @ file:///home/<USER>/feedstock_root/build_artifacts/certifi_1749972191589/work/certifi
cffi @ file:///croot/cffi_1736182485317/work
chamfer==2.0.0
chardet==5.2.0
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
click==8.2.1
cloudpickle==3.1.1
colorlog==6.9.0
comm==0.2.2
ConfigArgParse==1.7.1
contact-graspnet==0.0.0
contourpy==1.3.2
cryptography==45.0.4
cycler==0.12.1
dash==3.1.1
decorator==5.2.1
diffusers==0.34.0
distro==1.9.0
einops==0.8.1
embreex==2.17.7.post6
emd_ext==0.0.0
exceptiongroup==1.3.0
executing==2.2.0
Farama-Notifications==0.0.4
fasteners==0.19
fastjsonschema==2.21.1
filelock @ file:///croot/filelock_1744281381737/work
Flask==3.1.1
fonttools==4.58.4
fsspec==2025.5.1
gitdb==4.0.12
GitPython==3.1.44
gmpy2 @ file:///croot/gmpy2_1738085463648/work
gymnasium==0.29.1
h11==0.16.0
h5py==3.14.0
hf-xet==1.1.5
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.33.2
idna==3.10
imageio==2.34.2
imageio-ffmpeg==0.6.0
importlib_metadata==8.7.0
importlib_resources==6.5.2
intel-cmplr-lib-ur==2024.2.1
intel-openmp==2024.2.1
ipython==8.37.0
ipywidgets==8.1.7
isodate==0.7.2
itsdangerous==2.2.0
jedi==0.19.2
Jinja2 @ file:///croot/jinja2_1741710844255/work
jiter==0.10.0
joblib==1.5.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter_core==5.8.1
jupyterlab_widgets==3.0.15
kiwisolver==1.4.8
lazy_loader==0.4
lxml==6.0.0
manifold3d==3.1.1
MarkupSafe @ file:///croot/markupsafe_1738584038848/work
matplotlib==3.10.3
matplotlib-inline==0.1.7
mkl==2024.0.0
mkl-service==2.4.0
mkl_fft @ file:///croot/mkl_fft_1751317925336/work
mkl_random @ file:///croot/mkl_random_1751318527880/work
moviepy==2.2.1
mplib==0.2.1
mpmath @ file:///croot/mpmath_1690848262763/work
msal==1.32.3
msrest==0.7.1
msrestazure==0.6.4.post1
narwhals==1.45.0
nbformat==5.10.4
nest-asyncio==1.6.0
networkx @ file:///croot/networkx_1717597493534/work
numcodecs==0.13.1
numpy==1.26.4
numpy-quaternion==2024.0.9
-e git+https://github.com/NVlabs/curobo.git@cca894de9ec74e77a0a4071319c81958991a9108#egg=nvidia_curobo
oauthlib==3.3.1
open3d==0.18.0
openai==1.93.0
opencv-python==*********
packaging==25.0
pandas==2.3.0
parso==0.8.4
pexpect==4.9.0
pillow @ file:///croot/pillow_1744613067434/work
platformdirs==4.3.8
plotly==6.2.0
proglog==0.1.12
prompt_toolkit==3.0.51
protobuf==6.31.1
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
pybind11==2.13.6
pycollada==0.9.2
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pydantic==2.11.7
pydantic_core==2.33.2
pyglet==1.5.31
Pygments==2.19.2
PyJWT==2.10.1
pyparsing==3.2.3
pyperclip==1.9.0
pyquaternion==0.9.9
PySocks @ file:///home/<USER>/ci_310/pysocks_1640793678128/work
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
pytz==2025.2
PyYAML @ file:///croot/pyyaml_1728657952215/work
qwen-vl-utils==0.0.11
referencing==0.36.2
regex==2024.11.6
requests @ file:///croot/requests_1750426329888/work
requests-oauthlib==2.0.0
retrying==1.4.0
rpds-py==0.26.0
rtree==1.4.0
safetensors==0.5.3
sapien==3.0.0b1
scikit-image==0.25.2
scikit-learn==1.7.0
scipy==1.15.3
sentry-sdk==2.32.0
setuptools-scm==8.3.1
shapely==2.1.1
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
stack-data==0.6.3
svg.path==6.3
sympy @ file:///croot/sympy_1738108488918/work
tbb==2021.13.1
termcolor==3.1.0
threadpoolctl==3.6.0
tifffile==2025.5.10
timm==1.0.16
tokenizers==0.21.2
tomli==2.2.1
toppra==0.6.3
torch==2.4.1
torchaudio==2.4.1
torchvision==0.19.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.47.0
transforms3d==0.4.2
trimesh==4.4.3
triton==3.0.0
typing-inspection==0.4.1
typing_extensions @ file:///croot/typing_extensions_1734714854207/work
tzdata==2025.2
ur_rtde==1.5.9
urllib3 @ file:///croot/urllib3_1750775463400/work
vhacdx==0.0.8.post2
wandb==0.21.0
warp-lang==1.8.0
wcwidth==0.2.13
Werkzeug==3.1.3
widgetsnbextension==4.0.14
xatlas==0.0.10
xxhash==3.5.0
yourdfpy==0.0.58
zarr==2.18.3
zipp==3.23.0
