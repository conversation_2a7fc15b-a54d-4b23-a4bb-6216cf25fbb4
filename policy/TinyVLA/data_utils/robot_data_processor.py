import torch
import torchvision.transforms as T
from PIL import Image
from torchvision.transforms.functional import  InterpolationMode

def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    best_ratio_diff = float('inf')
    best_ratio = (1, 1)
    area = width * height
    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio

def dynamic_preprocess(image, min_num=1, max_num=12, image_size=448, use_thumbnail=False):
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    # calculate the existing image aspect ratio
    target_ratios = set(
        (i, j) for n in range(min_num, max_num + 1) for i in range(1, n + 1) for j in range(1, n + 1) if
        i * j <= max_num and i * j >= min_num)
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    # find the closest aspect ratio to the target
    target_aspect_ratio = find_closest_aspect_ratio(
        aspect_ratio, target_ratios, orig_width, orig_height, image_size)

    # calculate the target width and height
    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    # resize the image
    resized_img = image.resize((target_width, target_height))
    processed_images = []
    for i in range(blocks):
        box = (
            (i % (target_width // image_size)) * image_size,
            (i // (target_width // image_size)) * image_size,
            ((i % (target_width // image_size)) + 1) * image_size,
            ((i // (target_width // image_size)) + 1) * image_size
        )
        # split the image
        split_img = resized_img.crop(box)
        processed_images.append(split_img)
    assert len(processed_images) == blocks
    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)
    return processed_images

def load_image(image, transform, input_size=448, max_num=12):
    if isinstance(image, torch.Tensor):
        image = image.cpu().detach().numpy()
        if image.shape[0] == 3:
            image = image.transpose((1, 2, 0))
        image = Image.fromarray(image)
    images = dynamic_preprocess(image, image_size=input_size, use_thumbnail=False, max_num=max_num)
    pixel_values = [transform(image) for image in images]
    pixel_values = torch.stack(pixel_values)
    return pixel_values

class InternVL3Process:
    def __init__(
            self,
            tokenizer=None,
            conv_template=None,
            camera_names=None,
            data_args=None,
            num_image_token=256,
    ):
        super().__init__()
        self.tokenizer = tokenizer
        self.conv_template = conv_template
        self.num_image_token = num_image_token
        self.IMAGENET_MEAN = (0.485, 0.456, 0.406)
        self.IMAGENET_STD = (0.229, 0.224, 0.225)
        self.transform = T.Compose([
            T.Lambda(lambda img: img.convert('RGB') if img.mode != 'RGB' else img),
            T.Resize((448, 448), interpolation=InterpolationMode.BICUBIC),
            T.ToTensor(),
            T.Normalize(mean=self.IMAGENET_MEAN, std=self.IMAGENET_STD)
        ])
        self.IMG_CONTEXT_TOKEN = '<IMG_CONTEXT>'
        img_context_token_id = tokenizer.convert_tokens_to_ids(self.IMG_CONTEXT_TOKEN)
        self.img_context_token_id = img_context_token_id
        self.IMG_START_TOKEN = '<img>'
        self.IMG_END_TOKEN='</img>'

        self.camera_names = camera_names
        prefix = ""
        for cam_name in self.camera_names:
            prefix = prefix + cam_name + ": <image>\n"
        self.prefix = prefix
        self.data_args = data_args
        self.template = "<|im_start|>system\nYou are a helpful assistant.<|im_end|>\n<|im_start|>user\n{question}<|im_end|>\n<|im_start|>assistant\n"

    def preprocess_text(self, question, images, num_patches_list):
        question = question.replace('<image>', '')
        question = self.prefix + question
        query = self.template.format(question=question)
        for num_patches in num_patches_list:
            image_tokens = self.IMG_START_TOKEN + self.IMG_CONTEXT_TOKEN * self.num_image_token * num_patches + self.IMG_END_TOKEN
            query = query.replace('<image>', image_tokens, 1)
        return query

    def preprocess_image(self, image):
        return load_image(image, self.transform).to(torch.bfloat16)

    def preprocess(self, sample):
        data_dict = {}
        images = sample['image']
        question = sample['raw_lang']

        # preprocess image
        num_patches_list = []
        pixel_values = []
        for i in range(images.shape[0]):
            pixel_values.append(self.preprocess_image(images[i]))
            num_patches_list.append(pixel_values[-1].shape[0])
        pixel_values = torch.cat(pixel_values, dim=0)

        # preprocess text
        query = self.preprocess_text(question, images, num_patches_list)
        model_inputs = self.tokenizer(query, return_tensors='pt')

        input_ids = model_inputs['input_ids']
        attention_mask = model_inputs['attention_mask']

        data_dict['pixel_values'] = pixel_values
        data_dict['input_ids'] = input_ids
        data_dict['attention_mask'] = attention_mask
        data_dict['states'] = sample['state']
        if "action" in sample.keys():  # action and is_pad should be provided for policy training
            data_dict['actions'] = sample['action']
            data_dict['is_pad'] = sample['is_pad']
        return data_dict