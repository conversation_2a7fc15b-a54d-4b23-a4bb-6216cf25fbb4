name: RoboTwin
channels:
  - pytorch
  - nvidia
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main
  - conda-forge
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/linux-64/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/pytorch/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/
  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - aom=3.6.1=h59595ed_0
  - blas=1.0=mkl
  - brotlicffi=*******=py310h6a678d5_1
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.6.15=hbd8a1cb_0
  - certifi=2025.6.15=pyhd8ed1ab_0
  - cffi=1.17.1=py310h1fdaa30_1
  - cuda-cudart=11.8.89=0
  - cuda-cupti=11.8.87=0
  - cuda-libraries=11.8.0=0
  - cuda-nvrtc=11.8.89=0
  - cuda-nvtx=11.8.86=0
  - cuda-runtime=11.8.0=0
  - cuda-version=12.9=3
  - expat=2.7.1=h6a678d5_0
  - ffmpeg=4.4.2=gpl_hdf48244_113
  - font-ttf-dejavu-sans-mono=2.37=hab24e00_0
  - font-ttf-inconsolata=3.000=h77eed37_0
  - font-ttf-source-code-pro=2.038=h77eed37_0
  - font-ttf-ubuntu=0.83=h77eed37_3
  - fontconfig=2.14.2=h14ed4e7_0
  - fonts-conda-ecosystem=1=0
  - fonts-conda-forge=1=0
  - freetype=2.13.3=h4a9f257_0
  - gmp=6.3.0=h6a678d5_0
  - gmpy2=2.2.1=py310h5eee18b_0
  - gnutls=3.7.9=hb077bed_0
  - icu=73.2=h59595ed_0
  - jinja2=3.1.6=py310h06a4308_0
  - jpeg=9e=h5eee18b_3
  - lame=3.100=h7b6447c_0
  - lcms2=2.16=h92b89f2_1
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=4.0.0=h6a678d5_0
  - libcublas=*********=0
  - libcufft=*********=0
  - libcufile=1.14.1.1=4
  - libcurand=10.3.10.19=0
  - libcusolver=11.4.1.48=0
  - libcusparse=11.7.5.86=0
  - libdeflate=1.22=h5eee18b_0
  - libdrm=2.4.125=hb9d3cd8_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc=15.1.0=h767d61c_3
  - libgcc-ng=15.1.0=h69a702a_3
  - libgomp=15.1.0=h767d61c_3
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=11.8.0.86=0
  - libnsl=2.0.1=hb9d3cd8_1
  - libnvjpeg=11.9.0.86=0
  - libpciaccess=0.18=hb9d3cd8_0
  - libpng=1.6.39=h5eee18b_0
  - libsqlite=3.46.0=hde9e2c9_0
  - libstdcxx=15.1.0=h8f9b012_3
  - libstdcxx-ng=15.1.0=h4852527_3
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.7.0=hde9077f_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=2.38.1=h0b41bf4_0
  - libva=2.21.0=h4ab18f5_1
  - libvpx=1.13.1=h59595ed_0
  - libwebp-base=1.3.2=h5eee18b_1
  - libxcb=1.17.0=h9b100fa_0
  - libxcrypt=4.4.36=hd590300_1
  - libxml2=2.13.8=hfdd30dd_0
  - libzlib=1.2.13=h4ab18f5_6
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - markupsafe=3.0.2=py310h5eee18b_0
  - mkl-service=2.4.0=py310h5eee18b_3
  - mkl_fft=1.3.11=py310hacdc0fc_1
  - mkl_random=1.2.8=py310h2fd27a0_1
  - mpc=1.3.1=h5eee18b_0
  - mpfr=4.2.1=h5eee18b_0
  - mpmath=1.3.0=py310h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.9.1=h7ab15ed_0
  - openh264=2.3.1=hcb278e6_2
  - openjpeg=2.5.2=h0d4d230_1
  - openssl=3.5.1=h7b32b05_0
  - p11-kit=0.24.1=hc5aa10d_0
  - pip=25.1=pyhc872135_2
  - pthread-stubs=0.3=h0ce48e5_1
  - pysocks=1.7.1=py310h06a4308_0
  - python=3.10.13=hd12c33a_1_cpython
  - pytorch=2.4.1=py3.10_cuda11.8_cudnn9.1.0_0
  - pytorch-cuda=11.8=h7e8668a_6
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.2=py310h5eee18b_0
  - readline=8.2=h5eee18b_0
  - requests=2.32.4=py310h06a4308_0
  - setuptools=78.1.1=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - svt-av1=1.4.1=hcb278e6_0
  - tbb-devel=2022.0.0=hdb19cb5_0
  - tk=8.6.14=h993c535_1
  - torchaudio=2.4.1=py310_cu118
  - torchtriton=3.0.0=py310
  - torchvision=0.19.1=py310_cu118
  - typing_extensions=4.12.2=py310h06a4308_0
  - urllib3=2.5.0=py310h06a4308_0
  - wheel=0.45.1=py310h06a4308_0
  - x264=1!164.3095=h166bdaf_2
  - x265=3.5=h924138e_3
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-libxext=1.3.6=hb9d3cd8_0
  - xorg-libxfixes=6.0.1=hb9d3cd8_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - zlib=1.2.13=h4ab18f5_6
  - zstd=1.5.6=hc292b87_0
  - pip:
      - accelerate==0.26.0
      - adal==1.2.7
      - addict==2.4.0
      - annotated-types==0.7.0
      - anyio==4.9.0
      - asciitree==0.3.3
      - asttokens==3.0.0
      - attrs==25.3.0
      - av==14.4.0
      - azure==4.0.0
      - azure-ai-inference==1.0.0b9
      - azure-applicationinsights==0.1.1
      - azure-batch==4.1.3
      - azure-common==1.1.28
      - azure-core==1.34.0
      - azure-cosmosdb-nspkg==2.0.2
      - azure-cosmosdb-table==1.0.6
      - azure-datalake-store==0.0.53
      - azure-eventgrid==1.3.0
      - azure-graphrbac==0.40.0
      - azure-keyvault==1.1.0
      - azure-loganalytics==0.1.1
      - azure-mgmt==4.0.0
      - azure-mgmt-advisor==1.0.1
      - azure-mgmt-applicationinsights==0.1.1
      - azure-mgmt-authorization==0.50.0
      - azure-mgmt-batch==5.0.1
      - azure-mgmt-batchai==2.0.0
      - azure-mgmt-billing==0.2.0
      - azure-mgmt-cdn==3.1.0
      - azure-mgmt-cognitiveservices==3.0.0
      - azure-mgmt-commerce==1.0.1
      - azure-mgmt-compute==4.6.2
      - azure-mgmt-consumption==2.0.0
      - azure-mgmt-containerinstance==1.5.0
      - azure-mgmt-containerregistry==2.8.0
      - azure-mgmt-containerservice==4.4.0
      - azure-mgmt-cosmosdb==0.4.1
      - azure-mgmt-datafactory==0.6.0
      - azure-mgmt-datalake-analytics==0.6.0
      - azure-mgmt-datalake-nspkg==3.0.1
      - azure-mgmt-datalake-store==0.5.0
      - azure-mgmt-datamigration==1.0.0
      - azure-mgmt-devspaces==0.1.0
      - azure-mgmt-devtestlabs==2.2.0
      - azure-mgmt-dns==2.1.0
      - azure-mgmt-eventgrid==1.0.0
      - azure-mgmt-eventhub==2.6.0
      - azure-mgmt-hanaonazure==0.1.1
      - azure-mgmt-iotcentral==0.1.0
      - azure-mgmt-iothub==0.5.0
      - azure-mgmt-iothubprovisioningservices==0.2.0
      - azure-mgmt-keyvault==1.1.0
      - azure-mgmt-loganalytics==0.2.0
      - azure-mgmt-logic==3.0.0
      - azure-mgmt-machinelearningcompute==0.4.1
      - azure-mgmt-managementgroups==0.1.0
      - azure-mgmt-managementpartner==0.1.1
      - azure-mgmt-maps==0.1.0
      - azure-mgmt-marketplaceordering==0.1.0
      - azure-mgmt-media==1.0.1
      - azure-mgmt-monitor==0.5.2
      - azure-mgmt-msi==0.2.0
      - azure-mgmt-network==2.7.0
      - azure-mgmt-notificationhubs==2.1.0
      - azure-mgmt-nspkg==3.0.2
      - azure-mgmt-policyinsights==0.1.0
      - azure-mgmt-powerbiembedded==2.0.0
      - azure-mgmt-rdbms==1.9.0
      - azure-mgmt-recoveryservices==0.3.0
      - azure-mgmt-recoveryservicesbackup==0.3.0
      - azure-mgmt-redis==5.0.0
      - azure-mgmt-relay==0.1.0
      - azure-mgmt-reservations==0.2.1
      - azure-mgmt-resource==2.2.0
      - azure-mgmt-scheduler==2.0.0
      - azure-mgmt-search==2.1.0
      - azure-mgmt-servicebus==0.5.3
      - azure-mgmt-servicefabric==0.2.0
      - azure-mgmt-signalr==0.1.1
      - azure-mgmt-sql==0.9.1
      - azure-mgmt-storage==2.0.0
      - azure-mgmt-subscription==0.2.0
      - azure-mgmt-trafficmanager==0.50.0
      - azure-mgmt-web==0.35.0
      - azure-nspkg==3.0.2
      - azure-servicebus==0.21.1
      - azure-servicefabric==*******
      - azure-servicemanagement-legacy==0.20.8
      - azure-storage-blob==1.5.0
      - azure-storage-common==1.4.2
      - azure-storage-file==1.4.0
      - azure-storage-queue==1.4.0
      - blinker==1.9.0
      - chardet==5.2.0
      - charset-normalizer==3.4.2
      - click==8.2.1
      - cloudpickle==3.1.1
      - colorlog==6.9.0
      - comm==0.2.2
      - configargparse==1.7.1
      - contourpy==1.3.2
      - cryptography==45.0.4
      - cycler==0.12.1
      - dash==3.1.1
      - decorator==5.2.1
      - diffusers==0.34.0
      - distro==1.9.0
      - einops==0.8.1
      - embreex==2.17.7.post6
      - exceptiongroup==1.3.0
      - executing==2.2.0
      - farama-notifications==0.0.4
      - fasteners==0.19
      - fastjsonschema==2.21.1
      - filelock==3.18.0
      - flask==3.1.1
      - fonttools==4.58.4
      - fsspec==2025.5.1
      - gitdb==4.0.12
      - gitpython==3.1.44
      - gymnasium==0.29.1
      - h11==0.16.0
      - h5py==3.14.0
      - hf-xet==1.1.5
      - httpcore==1.0.9
      - httpx==0.28.1
      - huggingface-hub==0.33.2
      - idna==3.10
      - imageio==2.34.2
      - imageio-ffmpeg==0.6.0
      - importlib-metadata==8.7.0
      - importlib-resources==6.5.2
      - intel-cmplr-lib-ur==2024.2.1
      - intel-openmp==2024.2.1
      - ipython==8.37.0
      - ipywidgets==8.1.7
      - isodate==0.7.2
      - itsdangerous==2.2.0
      - jedi==0.19.2
      - jiter==0.10.0
      - joblib==1.5.1
      - jsonschema==4.24.0
      - jsonschema-specifications==2025.4.1
      - jupyter-core==5.8.1
      - jupyterlab-widgets==3.0.15
      - kiwisolver==1.4.8
      - lazy-loader==0.4
      - lxml==6.0.0
      - manifold3d==3.1.1
      - matplotlib==3.10.3
      - matplotlib-inline==0.1.7
      - mkl==2024.0.0
      - moviepy==2.2.1
      - mplib==0.2.1
      - msal==1.32.3
      - msrest==0.7.1
      - msrestazure==0.6.4.post1
      - narwhals==1.45.0
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.4.2
      - numcodecs==0.13.1
      - numpy==1.26.4
      - numpy-quaternion==2024.0.9
      - nvidia-curobo==0.7.7.post1.dev2+dirty
      - oauthlib==3.3.1
      - open3d==0.18.0
      - openai==1.93.0
      - opencv-python==*********
      - packaging==25.0
      - pandas==2.3.0
      - parso==0.8.4
      - pexpect==4.9.0
      - pillow==11.3.0
      - platformdirs==4.3.8
      - plotly==6.2.0
      - proglog==0.1.12
      - prompt-toolkit==3.0.51
      - protobuf==6.31.1
      - psutil==7.0.0
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - pybind11==2.13.6
      - pycollada==0.9.2
      - pycparser==2.22
      - pydantic==2.11.7
      - pydantic-core==2.33.2
      - pyglet==1.5.31
      - pygments==2.19.2
      - pyjwt==2.10.1
      - pyparsing==3.2.3
      - pyperclip==1.9.0
      - pyquaternion==0.9.9
      - python-dateutil==2.9.0.post0
      - python-dotenv==1.1.1
      - pytz==2025.2
      - qwen-vl-utils==0.0.11
      - referencing==0.36.2
      - regex==2024.11.6
      - requests-oauthlib==2.0.0
      - retrying==1.4.0
      - rpds-py==0.26.0
      - rtree==1.4.0
      - safetensors==0.5.3
      - sapien==3.0.0b1
      - scikit-image==0.25.2
      - scikit-learn==1.7.0
      - scipy==1.15.3
      - sentry-sdk==2.32.0
      - setuptools-scm==8.3.1
      - shapely==2.1.1
      - six==1.17.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - stack-data==0.6.3
      - svg-path==6.3
      - sympy==1.14.0
      - tbb==2021.13.1
      - termcolor==3.1.0
      - threadpoolctl==3.6.0
      - tifffile==2025.5.10
      - timm==1.0.16
      - tokenizers==0.21.2
      - tomli==2.2.1
      - toppra==0.6.3
      - tqdm==4.67.1
      - traitlets==5.14.3
      - transformers==4.47.0
      - transforms3d==0.4.2
      - trimesh==4.4.3
      - triton==3.0.0
      - typing-extensions==4.14.0
      - typing-inspection==0.4.1
      - tzdata==2025.2
      - vhacdx==0.0.8.post2
      - wandb==0.21.0
      - warp-lang==1.8.0
      - wcwidth==0.2.13
      - werkzeug==3.1.3
      - widgetsnbextension==4.0.14
      - xatlas==0.0.10
      - xxhash==3.5.0
      - yourdfpy==0.0.58
      - zarr==2.18.3
      - zipp==3.23.0

