name: task_config

image_shape: &image_shape [3, -1, -1]
shape_meta: &shape_meta
  # acceptable types: rgb, low_dim
  obs:
    head_cam:
      shape: *image_shape
      type: rgb
    # front_cam:
    #   shape: *image_shape
    #   type: rgb
    # left_cam:
    #   shape: *image_shape
    #   type: rgb
    # right_cam:
    #   shape: *image_shape
    #   type: rgb
    agent_pos:
      shape: [16]
      type: low_dim
  action:
    shape: [16]

env_runner:
  _target_: diffusion_policy.env_runner.pusht_image_runner.PushTImageRunner
  n_train: 6
  n_train_vis: 2
  train_start_seed: 0
  n_test: 50
  n_test_vis: 4
  legacy_test: True
  test_start_seed: 100000
  max_steps: 300
  n_obs_steps: ${n_obs_steps}
  n_action_steps: ${n_action_steps}
  fps: 10
  past_action: ${past_action_visible}
  n_envs: null

dataset:
  _target_: diffusion_policy.dataset.robot_image_dataset.RobotImageDataset
  zarr_path: data/useless.zarr
  batch_size: ${dataloader.batch_size}
  horizon: ${horizon}
  pad_before: ${eval:'${n_obs_steps}-1'}
  pad_after: ${eval:'${n_action_steps}-1'}
  seed: 42
  val_ratio: 0.02
  max_train_episodes: null
